# Simple Role Syntax
# ==================
# Supports bulk-adding hosts to roles, the primary
# server in each group is considered to be the first
# unless any hosts have the primary property set.
# Don't declare `role :all`, it's a meta role

# Default branch is :staging-3
set :branch, 'staging-3'

set :deploy_to, "/var/www/qa-stellantis.gestaotarifaria.com.br/production/"

role :app, %w{facedigital@*************}
role :web, %w{facedigital@*************}

server '*************',
   roles: %w{web app},
   ssh_options: {
     user: 'facedigital', # overrides user setting above
     keys: %w(~/.ssh/id_rsa),
     forward_agent: true,
     auth_methods: %w(publickey),
   }