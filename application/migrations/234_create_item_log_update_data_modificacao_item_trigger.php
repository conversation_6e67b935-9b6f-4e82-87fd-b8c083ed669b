<?php

class Migration_Create_Item_Log_Update_Data_Modificacao_Item_Trigger extends CI_Migration
{

    private $_table = "item_log";
    public function up()
    {
        if ($this->db->table_exists($this->_table)) {
            $fields = array();

            if (!$this->db->field_exists('nao_atualiza_item', $this->_table)) {
                $fields['nao_atualiza_item'] = array(
                    'type'           => 'tinyint(1)',
                    'null'           => TRUE,
                    'default'        => 0
                );
            }

            if (!empty($fields)) $this->dbforge->add_column($this->_table, $fields);
        }

        $this->db->query("
                    CREATE TRIGGER `item_log_after_update`
                    AFTER INSERT ON item_log
                    FOR EACH ROW
                    BEGIN
                        DECLARE diff_seconds INT;
                        DECLARE item_modification_time TIMESTAMP;
                        IF NEW.nao_atualiza_item = 0 THEN
	                        SELECT data_modificacao INTO item_modification_time
	                        FROM item
	                        WHERE part_number = NEW.part_number
	                        AND id_empresa = NEW.id_empresa
	                        AND estabelecimento = NEW.estabelecimento;
	                        
	                        IF item_modification_time IS NULL THEN
	                            SET diff_seconds = 0;
	                        ELSE
	                            SET diff_seconds = TIMESTAMPDIFF(SECOND, item_modification_time, CURRENT_TIMESTAMP);
	                        END IF;
	                        
	                        IF diff_seconds > 2 OR item_modification_time IS NULL THEN
	                            UPDATE item
	                            SET data_modificacao = CURRENT_TIMESTAMP
	                            WHERE part_number = NEW.part_number
	                            AND id_empresa = NEW.id_empresa
	                            AND estabelecimento = NEW.estabelecimento;
	                        END IF;
	                     END IF;
                    END
                    ");

        $this->db->query("
                    CREATE TRIGGER cad_item_attr_after_update AFTER
                    UPDATE ON cad_item_attr FOR EACH ROW 
                    BEGIN
                        UPDATE
                            item i
                        INNER JOIN cad_item ci ON
                            ci.part_number = i.part_number
                            AND ci.id_empresa = i.id_empresa
                            AND ci.estabelecimento = i.estabelecimento SET
                            i.data_modificacao = CURRENT_TIMESTAMP
                        WHERE
                            ci.id_item = NEW.id_item;
                    END;  
                    ");         
                    
            $this->db->query("DROP TRIGGER `update_item_status_homologacao_em_revisao`");

            $this->db->query("
                    CREATE TRIGGER update_item_status_homologacao_em_revisao
                    BEFORE UPDATE ON item
                    FOR EACH ROW
                    BEGIN
                    DECLARE status_anterior VARCHAR(255);
                    DECLARE status_atual VARCHAR(255);
                    DECLARE has_owner_ativo VARCHAR(255);

                        SELECT ifnull(id_empresa, 0) INTO has_owner_ativo FROM empresa WHERE empresa.id_empresa = NEW.id_empresa AND empresa.campos_adicionais LIKE '%owner%';

                        IF OLD.id_status = 2 AND NEW.descricao <> OLD.descricao AND has_owner_ativo > 0 THEN
                        
                        SET NEW.id_status = 10;

                        SELECT status INTO status_anterior FROM status WHERE id = OLD.id_status;
                        SELECT status INTO status_atual FROM status WHERE id = NEW.id_status;

                        INSERT INTO item_log
                            SET part_number = OLD.part_number,
                                estabelecimento = OLD.estabelecimento,
                                id_empresa = OLD.id_empresa,
                                id_item = 0,
                                tipo_homologacao = ifnull(@user_update_cad_item_homologacao, 'Engenharia'),
                                id_usuario = COALESCE(@user_id, ifnull(@user_update_cad_item_homologacao, COALESCE(OLD.id_resp_engenharia, 0))),
                                titulo = 'atualizacao',
                                motivo = CONCAT('Alteração do Status: <em>', status_anterior, '</em> &rarr; <strong>', status_atual, '</strong>') ,
                                criado_em = NOW(),
                                nao_atualiza_item = 1;

                        INSERT INTO log_descricao_item
                            SET part_number = OLD.part_number,
                                estabelecimento = OLD.estabelecimento,
                                id_empresa = OLD.id_empresa,
                                descricao_anterior = OLD.descricao,
                                descricao_nova = NEW.descricao,
                                data_alteracao = NOW();
                    END IF;
                END
            ");

    }

    public function down()
    {
        if ($this->db->table_exists($this->_table)) {

            if ($this->db->field_exists('nao_atualiza_item', $this->_table)) {

                $this->dbforge->drop_column($this->_table, 'nao_atualiza_item');

            }

        }
        $this->db->query("DROP TRIGGER `item_log_after_update`");
        $this->db->query("DROP TRIGGER `cad_item_attr_after_update`");
        $this->db->query("DROP TRIGGER `update_item_status_homologacao_em_revisao`");

        $this->db->query("
                CREATE TRIGGER update_item_status_homologacao_em_revisao
                BEFORE UPDATE ON item
                FOR EACH ROW
                BEGIN
                DECLARE status_anterior VARCHAR(255);
                DECLARE status_atual VARCHAR(255);
                DECLARE has_owner_ativo VARCHAR(255);

                    SELECT ifnull(id_empresa, 0) INTO has_owner_ativo FROM empresa WHERE empresa.id_empresa = NEW.id_empresa AND empresa.campos_adicionais LIKE '%owner%';

                    IF OLD.id_status = 2 AND NEW.descricao <> OLD.descricao AND has_owner_ativo > 0 THEN
                    
                    SET NEW.id_status = 10;

                    SELECT status INTO status_anterior FROM status WHERE id = OLD.id_status;
                    SELECT status INTO status_atual FROM status WHERE id = NEW.id_status;

                    INSERT INTO item_log
                        SET part_number = OLD.part_number,
                            estabelecimento = OLD.estabelecimento,
                            id_empresa = OLD.id_empresa,
                            id_item = 0,
                            tipo_homologacao = ifnull(@user_update_cad_item_homologacao, 'Engenharia'),
                            id_usuario = COALESCE(@user_id, ifnull(@user_update_cad_item_homologacao, COALESCE(OLD.id_resp_engenharia, 0))),
                            titulo = 'atualizacao',
                            motivo = CONCAT('Alteração do Status: <em>', status_anterior, '</em> &rarr; <strong>', status_atual, '</strong>') ,
                            criado_em = NOW();

                    INSERT INTO log_descricao_item
                        SET part_number = OLD.part_number,
                            estabelecimento = OLD.estabelecimento,
                            id_empresa = OLD.id_empresa,
                            descricao_anterior = OLD.descricao,
                            descricao_nova = NEW.descricao,
                            data_alteracao = NOW();
                END IF;
            END
        ");

    }
}
    