<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Migration_Add_Index_Sla extends CI_Migration {

    public function up()
    {
        if ($this->db->table_exists('item_log_status_sla')) {
            $this->db->query("ALTER TABLE `item_log_status_sla` ADD INDEX `idx_part_number_id_empresa_estabelecimento` (`part_number` ASC, `id_empresa` ASC, `estabelecimento` ASC) ");
            $this->db->query("ALTER TABLE `item_log_status_sla` ADD INDEX `idx_item_log_status_sla_composite` (`part_number` ASC, `id_empresa` ASC, `estabelecimento` ASC, `data_status_anterior` ASC, `data_novo_status` ASC) ");
            $this->db->query("ALTER TABLE `item_log_status_sla` ADD INDEX `idx_item_log_status_sla_novo_status` (`novo_status_descricao` ASC, `data_novo_status` ASC, `data_status_anterior` ASC ) ");
        }

        if ($this->db->table_exists('item')) {
            $this->db->query("ALTER TABLE `item` ADD INDEX `idx_status_empresa` (`id_status` ASC, `id_empresa` ASC) ");
            $this->db->query("ALTER TABLE `item` ADD INDEX `idx_empresa_status` (`id_empresa` ASC, `id_status` ASC) ");
            $this->db->query("ALTER TABLE `item` ADD INDEX `idx_item_usuario_bloqueador` (`usuario_bloqueador` ASC) ");
        }

        if ($this->db->table_exists('empresa')) {
            $this->db->query("ALTER TABLE `item` ADD INDEX `idx_empresa_id_squad` (`id_squad` ASC) ");
        } 
    }
    
    public function down()
    {
        if ($this->db->table_exists('item_log_status_sla')) {
            $this->db->query("DROP INDEX `idx_part_number_id_empresa_estabelecimento` ON `item_log_status_sla`");
            $this->db->query("DROP INDEX `idx_item_log_status_sla_composite` ON `item_log_status_sla`");
            $this->db->query("DROP INDEX `idx_item_log_status_sla_novo_status` ON `item_log_status_sla`");
        } 

        if ($this->db->table_exists('item')) {
            $this->db->query("DROP INDEX `idx_status_empresa` ON `item`");
            $this->db->query("DROP INDEX `idx_empresa_status` ON `item`");
            $this->db->query("DROP INDEX `idx_item_usuario_bloqueador` ON `item`");
        }

        if ($this->db->table_exists('empresa')) {
            $this->db->query("DROP INDEX `idx_empresa_id_squad` ON `empresa`");
        }
    }

}
