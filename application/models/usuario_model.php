<?php

class Usuario_model extends MY_Model {

	public $_table = 'usuario';

	public function __construct() {
		parent::__construct();
	}

    public function get_user_id_by_email($email)
    {
        $this->db->where('email', $email);
        $query = $this->db->get($this->_table, 1);

        if ($query->num_rows() > 0) {
            $row = $query->row();
            return $row->id_usuario;
        }

        return FALSE;
    }

    public function get_email_user($id)
    {
        $this->db->select('usuario.email');
        $this->db->where('id_usuario', $id);

        $query = $this->db->get($this->_table);
        if ($query->num_rows()) {
            $row = $query->row();
            return $row->email;
        }

        return NULL;
    }

	public function check_login($email, $senha)
	{
		$query = $this->db->get_where($this->_table, array('email' => $email), 1);

		if ($query->num_rows())
        {
			$row = $query->row();

            if (! $this->password_verify($senha, $row->senha))
                return FALSE;

			$this->session->set_userdata('user_id', $row->id_usuario);
			$this->session->set_userdata('user_nome', $row->nome);

			return TRUE;
		}

		return FALSE;
	}

    public function check_empresa_ativa($username)
    {
        $this->db->where('u.email', $username);
        $this->db->where('e.ativo', 1);
        $this->db->join('empresa e', 'u.id_empresa = e.id_empresa', 'left');
        $query = $this->db->get($this->_table . ' u');
        if ($query->num_rows()) {
            return true;
        }

        return false;

    }

    public function password_hash($passwd, $sha1 = TRUE)
    {
        $passwordstr = ($sha1 == TRUE ? sha1($passwd) : $passwd);
        return password_hash($passwordstr, PASSWORD_BCRYPT);
    }

    public function password_verify($passwd, $hash)
    {
        return password_verify(sha1($passwd), $hash);
    }


    public function get_total_entries()
    {
        $this->db->select('count(distinct u.id_usuario) as total', false);

        $this->db->join('perfil p', 'u.id_perfil = p.id_perfil', 'inner');
        $this->db->join('empresa e', 'u.id_empresa = e.id_empresa', 'inner');
        $this->db->join('usuario_empresa up', 'up.id_usuario = u.id_usuario', 'left');

        if ($id_empresa = $this->get_state('filter.id_empresa')) {
            $this->db->where("(u.id_empresa = '{$id_empresa}' OR up.id_empresa = '{$id_empresa}')", NULL, TRUE);
        }

        if ($nome = $this->get_state('filter.nome')) {
            $this->db->like('u.nome', $nome);
        }

        if ($perfil = $this->get_state('filter.perfil')) {
            $this->db->where_in('u.id_perfil', $perfil);
        }

        $query = $this->db->get($this->_table . ' u');

        return $query->row()->total;
    }

	public function get_entries($limit = NULL, $offset = NULL)
	{
		$this->db->select('u.*, p.descricao AS perfil, e.razao_social as empresa');
        $this->db->join('perfil p', 'u.id_perfil = p.id_perfil', 'inner');
        $this->db->join('empresa e', 'u.id_empresa = e.id_empresa', 'inner');
		$this->db->join('usuario_empresa up', 'up.id_usuario = u.id_usuario', 'left');

        if ($id_empresa = $this->get_state('filter.id_empresa')) {
            $this->db->where("(u.id_empresa = '{$id_empresa}' OR up.id_empresa = '{$id_empresa}')", NULL, TRUE);
        }

        if ($nome = $this->get_state('filter.nome')) {
            $this->db->like('u.nome', $nome);
        }

        if ($perfil = $this->get_state('filter.perfil')) {
            $this->db->where_in('u.id_perfil', $perfil);
        }

        if ($search = $this->get_state('filter.search')) {
            $this->db->like("u.nome", $search);
        }

		if ($order_by_arr = $this->get_state('order_by')) {
			$order_output = implode(', ', array_map(
				function ($v, $k) {
					return sprintf("%s %s", $k, $v);
				}, $order_by_arr, array_keys($order_by_arr)
			));

			$this->db->order_by($order_output);
		} else {
			$this->db->order_by('nome', 'ASC');
		}

        $this->db->group_by('u.id_usuario');
		$query = $this->db->get($this->_table . ' u', $limit, $offset);
        
		return $query->result();
	}

    public function get_responsaveis($role, $por_empresa = NULL, $perfil = FALSE, $usuario = null)
    {
        if ($id_empresa = $this->get_state('filter.id_empresa'))
        {
            if (empty($id_empresa))
            {
                $id_empresa = sess_user_company();
            }
            $this->db->where('ue.id_empresa', $id_empresa);
        } else if ($por_empresa != NULL) {
            $this->db->where('ue.id_empresa', $por_empresa);
        }
        
        $select = $perfil ? ", pe.descricao AS perfil_descricao" : "";

        $this->db->select("u.*, p.descricao AS perfil{$select}");

        if ($perfil) {
            $this->db->join('perfil pe', 'pe.id_perfil = u.id_perfil', 'inner');
        }

        $this->db->join('perfil_permissao pp', 'pp.id_perfil = u.id_perfil', 'inner');
        $this->db->join('permissao p', 'p.id_permissao = pp.id_permissao', 'inner');
        $this->db->join('usuario_empresa ue', 'ue.id_usuario = u.id_usuario', 'inner');
        $this->db->where('slug', $role);
        $where_user = '';
        if (!empty($usuario))
        {
            $where_user = "OR (u.id_usuario = {$usuario})";
        }
        $this->db->or_where("(u.id_usuario = (SELECT id_gerente_de_projetos FROM empresa WHERE id_empresa = '{$id_empresa}') AND slug = 'cliente_pmo') {$where_user}", NULL, TRUE);

        if ($order_by_arr = $this->get_state('order_by')) {
            $order_output = implode(', ', array_map(
                function ($v, $k) {
                    return sprintf("%s %s", $k, $v);
                }, $order_by_arr, array_keys($order_by_arr)
            ));

            $this->db->order_by($order_output);
        } else {
            $this->db->order_by('nome', 'ASC');
        }

        $this->db->group_by('u.id_usuario');

        $query = $this->db->get($this->_table.' u');
        return $query->result();
    }

    public function get_entries_by_role($role, $por_empresa = NULL, $perfil = FALSE, $usuario = null)
    {
        if ($id_empresa = $this->get_state('filter.id_empresa'))
        {
            if (empty($id_empresa))
            {
                $id_empresa = sess_user_company();
            }
            $this->db->where('u.id_empresa', $id_empresa);
        } else if ($por_empresa != NULL) {
            $this->db->where('u.id_empresa', $por_empresa);
        }
        
        $select = $perfil ? ", pe.descricao AS perfil_descricao" : "";

        $this->db->select("u.*, p.descricao AS perfil{$select}");

        if ($perfil) {
            $this->db->join('perfil pe', 'pe.id_perfil = u.id_perfil', 'inner');
        }

        $this->db->join('perfil_permissao pp', 'pp.id_perfil = u.id_perfil', 'inner');
        $this->db->join('permissao p', 'p.id_permissao = pp.id_permissao', 'inner');

        $this->db->where('slug', $role);
        $where_user = '';
        if (!empty($usuario))
        {
            $where_user = "OR (u.id_usuario = {$usuario})";
        }
        $this->db->or_where("(u.id_usuario = (SELECT id_gerente_de_projetos FROM empresa WHERE id_empresa = '{$id_empresa}') AND slug = 'cliente_pmo') {$where_user}", NULL, TRUE);

        if ($order_by_arr = $this->get_state('order_by')) {
            $order_output = implode(', ', array_map(
                function ($v, $k) {
                    return sprintf("%s %s", $k, $v);
                }, $order_by_arr, array_keys($order_by_arr)
            ));

            $this->db->order_by($order_output);
        } else {
            $this->db->order_by('nome', 'ASC');
        }

        $this->db->group_by('u.id_usuario');

        $query = $this->db->get($this->_table.' u');
        return $query->result();
    }

    public function get_gp_by_empresa($id_empresa)
    {
        $this->db->select('u.*');
        $this->db->where("u.id_usuario = (SELECT id_gerente_de_projetos FROM empresa WHERE id_empresa = '{$id_empresa}')", NULL, TRUE);

        $query = $this->db->get($this->_table.' u');

        return $query->row();
    }

	public function get_entry($id_usuario)
	{
		$this->db->where('id_usuario', $id_usuario);
		$query = $this->db->get($this->_table);
        if ($query->num_rows() > 0) {
		    return $query->row();
        }

        throw new Exception('C�digo de usu�rio inexistente.');
	}

    public function get_usuario_mail($email)
    {
		$this->db->where('email', $email);
		$query = $this->db->get($this->_table);
        return $query->row();
    }

    public function get_mail($id_empresa)
	{
        $result = $this->db->query("
        SELECT email,id_empresa, id_usuario FROM usuario 
           where 
           SUBSTRING_INDEX(
               SUBSTRING(
                   SUBSTRING_INDEX(email, '.com', 1),
                   LOCATE('@', email) + 1
               ),
                   '.',
                   1
           ) = 'becomex' and recebe_email_alteracao_status = '1' or  id_empresa = {$id_empresa} and recebe_email_alteracao_status = '1'
        ");

		return $result->result();
	}

    public function save_config_mail($data, $id_empresa)
	{
        $this->db->where('id_empresa', $id_empresa);
        $this->db->delete('rel_status');

        return $this->db->insert_batch('rel_status', $data);
    }

	public function get_user_company($id_usuario)
	{
		$this->db->select('id_empresa');
		$this->db->where('id_usuario', $id_usuario);
		$query = $this->db->get($this->_table);
		if ($query->num_rows() > 0) {
			$row = $query->row();
			return $row->id_empresa;
		}

		return NULL;
	}

	public function remove($id_list)
	{
		if (is_array($id_list) && count($id_list) > 0) {
			$this->db->where_in('id_usuario', $id_list);
			return $this->db->delete($this->_table);
		}

		return FALSE;
	}

    public function get_gps_by_empresa($id_empresa)
    {
        $this->db->where('id_empresa', $id_empresa);

        $this->db->join('perfil_permissao pp', 'pp.id_perfil = u.id_perfil', 'inner');
        $this->db->join('permissao p', 'pp.id_permissao = p.id_permissao', 'inner');

        $this->db->where('slug', 'cliente_pmo');

        $query = $this->db->get($this->_table.' u');

        return $query->result();
    }

    public function get_empresas_by_user($id_usuario, $id_only = FALSE)
    {
        $this->db->select('e.*, u.id_usuario');
        $this->db->where('u.id_usuario', $id_usuario);

        $this->db->join('usuario_empresa ue', 'ue.id_usuario = u.id_usuario');
        $this->db->join('empresa e', 'e.id_empresa = ue.id_empresa');

        $query = $this->db->get($this->_table.' u');

        $res = $query->result();

        if ($id_only === TRUE)
        {
            $arr = array();
            foreach ($res as $row) {
                $arr[] = $row->id_empresa;
            }
            return $arr;
        }else
        {
            return $res;
        }
    }

    public function check_permissao_empresa($id_empresa, $id_usuario = null)
    {
        $id_usuario = ($id_usuario ? $id_usuario : sess_user_id());

        $this->db->where('ue.id_usuario', $id_usuario);
        $this->db->where('ue.id_empresa', $id_empresa);

        $query = $this->db->get('usuario_empresa ue');

        return (bool) $query->num_rows();
    }

    public function remove_usuario_empresas($id_usuario, $empresas_permitidas)
    {
        if (! has_role('sysadmin'))
        {
            if (count($empresas_permitidas) == 0) {
                return false;
            }

            $this->db->where_in('id_empresa', $empresas_permitidas);
        }

        $this->db->where('id_usuario', $id_usuario);
        return $this->db->delete('usuario_empresa');
    }

    public function save_usuario_empresas($id_usuario, $empresas)
    {
        if (is_array($empresas))
        {
            foreach ($empresas as $id_empresa)
            {
                $data = array(
                    'id_usuario' => $id_usuario,
                    'id_empresa' => $id_empresa
                );

                $this->db->insert('usuario_empresa', $data);
            }
        }else{
            $data = array(
                'id_usuario' => $id_usuario,
                'id_empresa' => $empresas
            );

            $this->db->insert('usuario_empresa', $data);
        }
    }

    public function get_entries_filtro_atribuido_para()
    {
        $id_empresa = $this->get_state('filter.id_empresa');

        if ($order_by_arr = $this->get_state('order_by')) {
            $order_output = implode(', ', array_map(
                function ($v, $k) {
                    return sprintf("%s %s", $k, $v);
                }, $order_by_arr, array_keys($order_by_arr)
            ));
        } else {
            $order_output = 'u.nome ASC';
        }
        // Mudança para permitir acessar a base de homologação com os recursos existentes.
        // Não foi possível confirmar a performance da nova query por isso foi necessário manter esse método
        // Implementação temporária - Data - Janeiro 2024
        if (ENVIRONMENT == 'development')
        {
            $query = $this->db->query("SELECT 
                u.id_usuario,
                u.nome,
                u.email,
                p.descricao AS perfil,
                COALESCE(t1.total_resp_engenharia, t1.total_resp_fiscal) AS total_resp,
                u.id_perfil
            FROM
                usuario u
            INNER JOIN (
                SELECT 
                    COALESCE(i.id_resp_engenharia, i.id_resp_fiscal) AS id_usuario,
                    SUM(CASE WHEN i.id_resp_engenharia IS NOT NULL THEN 1 ELSE 0 END) AS total_resp_engenharia,
                    SUM(CASE WHEN i.id_resp_fiscal IS NOT NULL THEN 1 ELSE 0 END) AS total_resp_fiscal
                FROM
                    item i
                LEFT JOIN cad_item c ON
                    c.id_empresa = i.id_empresa
                    AND c.part_number = i.part_number
                    AND c.estabelecimento = i.estabelecimento
                WHERE
                    i.id_empresa = {$id_empresa}
                    AND c.id_empresa IS NULL
                GROUP BY id_usuario
            ) t1 ON t1.id_usuario = u.id_usuario
            INNER JOIN perfil p ON p.id_perfil = u.id_perfil
            GROUP BY u.id_usuario
            ORDER BY {$order_output}");
        } else {
            $query = $this->db->query("
                select
                    u.id_usuario,
                    u.nome,
                    u.email,
                    p.descricao as perfil,
                    t1.total_resp,
                    u.id_perfil
                from
                    usuario u
                inner join
                (
                    select
                        i.id_resp_engenharia as id_usuario,
                        count(i.part_number) as total_resp
                    from
                    item i
                    where i.id_resp_engenharia is not null
                    and i.id_empresa = {$id_empresa}
                    and not exists (
                        select 1 from cad_item c
                        where (
                            c.id_empresa = i.id_empresa and
                            c.part_number = i.part_number and
                            c.estabelecimento = i.estabelecimento
                        )
                    )
                    group by id_resp_engenharia
    
                    union all
    
                    select
                        i.id_resp_fiscal as id_usuario,
                        count(i.part_number) as total_resp
                    from
                    item i
                    where i.id_resp_fiscal is not null
                    and i.id_empresa = {$id_empresa}
                    and not exists (
                        select 1 from cad_item c
                        where (
                            c.id_empresa = i.id_empresa and
                            c.part_number = i.part_number and
                            c.estabelecimento = i.estabelecimento
                        )
                    )
                    group by id_resp_fiscal
                ) t1
                on t1.id_usuario = u.id_usuario
                inner join perfil p
                on p.id_perfil = u.id_perfil
                group by u.id_usuario
                order by {$order_output}"
            );
        }

        return $query->result();
    }
}
