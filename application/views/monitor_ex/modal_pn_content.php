<ul class="nav nav-tabs" id="top-page">
    <li class="active">
        <a data-toggle="tab" href="#novo-movimento">Novo movimento</a>
    </li>
    <li>
        <a data-toggle="tab" href="#historico">Histórico</a>
    </li>
</ul>

<div class="tab-content">
    <div id="novo-movimento" class="tab-pane fade in active">
        <h3>Novo movimento</h3>
        <?php echo form_open('/monitor_ex/modal_pn_update_status', 'enctype="multipart/form-data"') ?>
        <input type="hidden" name="id_ctrl_ex" id="id_ctrl_ex" value="<?php echo $id_ctrl_ex; ?>">
        <input type="hidden" name="part_number" id="part_number" value="<?php echo $part_number; ?>">
        <div class="form-group">
            <label for="select-status" class="control-label">Selecione o Status <span class="required text-danger">*</span></label>
            <select data-live-search="true" class="selectpicker form-control" data-title="--" name="status_part_number" id="status_part_number">
                <?php if (!empty($status)) : $optgroup = '';
                    $first = true; ?>
                    <?php foreach ($status as $value) : ?>
                        <?php if (strtolower($value->estagio) != strtolower($optgroup)) : $optgroup = $value->estagio;
                            $close_opt = true; ?>
                            <?php if (!$first) : ?> </optgroup> <?php endif;
                                                            $first = false; ?>
                            <optgroup label="<?php echo $optgroup; ?>"> ?>
                            <?php endif; ?>
                            <option <?php if ($value->id_status == $status_atual) echo 'selected'; ?> value="<?php echo $value->id_status ?>"><?php echo $value->descricao ?></option>
                        <?php endforeach ?>
                    <?php endif; ?>
            </select>
        </div>

        <div class="form-group">
            <label for="" class="control-label">Atribuir Usuário ao movimento</label>
            <select class="selectpicker form-control" data-actions-box="true" name="id_usuario" id="id_usuario">
                <option value="">Selecione um Usuário</option>
                <?php foreach ($usuarios_select as $usuario) : ?>
                    <option value="<?php echo $usuario->id_usuario ?>"><?php echo $usuario->nome ?></option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="form-group" id="descricao_status_div">
            <label for="textarea-detalhes" class="control-label">Detalhes da movimentação <span class="required text-danger">*</span></label>
            <textarea style="height: 200px" required type="text" class="form-control " name="descricao_status" id="descricao_status"></textarea>
        </div>

        <div class="form-group">
            <label for="fileName">Anexar Arquivo:</label>
            <div class="custom-file">
                <input type="file" name="file" class="form-control">
            </div>
            <small>Tipos permitidos: xlsx | xls | docx | pdf | rar | msg | png | jpg | jpeg | txt</small>
        </div>

        <div class="form-group">
            <button disabled class="btn btn-primary" type="submit" name="salvar" id="salvar" value="1">
                <span class="glyphicon glyphicon-floppy-disk"></span>
                Salvar
            </button>
        </div>
        </form>
    </div>
    <div id="historico" class="tab-pane fade">
        <h3>Histórico</h3>
        <?php if (!empty($historico)) : ?>
            <?php foreach ($historico as $item) : ?>
                <h5>
                    <?php echo $item->nome_usuario; ?> - <small><?php echo $item->status_descricao ?></small>
                </h5>
                <?php if (isset($item->usuario_nome)) : ?>
                    <small>Atribuído ao usuário <?php echo $item->usuario_nome ?></small>
                <?php endif; ?>
                <p><?php echo $item->motivo; ?></p>
                <?php if ($item->id_arquivo != 0) : ?>
                    <a href="<?php echo base_url('monitor_ex/download/' . $item->id_arquivo) ?>">
                        <i class="glyphicon glyphicon-cloud-download"></i> Baixar Anexo do Movimento
                    </a>
                    -
                <?php endif; ?>
                <small><?php echo date('d/m/Y H:i:s', strtotime($item->criado_em)); ?></small>
                <hr>
            <?php endforeach; ?>
        <?php endif; ?>
        <p class="text-right">
            <a href="#top-page">Voltar ao topo <span class="glyphicon glyphicon-arrow-top"></span></a>
        </p>
    </div>
</div>

<script type="text/javascript">
    $(document).ready(function() {
        $('.custom-file-input').on('change', function() {
            let fileName = $(this).val().split('\\').pop();
            $(this).next().addClass("selected").html(fileName);
        });
    })
    $('.selectpicker').selectpicker();
    CKEDITOR.replace('descricao_status', {
        on: {
            change: function(evt) {
                if (evt.editor.getData() != "") {
                    $("#salvar").removeAttr('disabled');
                } else {
                    $("#salvar").attr('disabled', 'disabled');
                }
            }
        }
    });
</script>