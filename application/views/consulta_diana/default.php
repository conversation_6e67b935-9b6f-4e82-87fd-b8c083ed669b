<div class="container">
	<div class="row">
		<form id="search-api-form" action="<?php echo base_url('consulta_diana') ?>" method="get">
			<div class="col-md-10 col-md-offset-1 filter-consulta">
				<h1 class="text-center logo-diana">
					<img src="<?php echo base_url('assets/img/diana/diana.png') ?>" alt="Diana">
				</h1>
				
				<?php if (!empty($result) && empty($status_api)): ?>
				<div class="alert alert-warning alert-dismissible show" role="alert">
					<strong>Erro!</strong> não é possivel realizar pesquisas no momento, tente novamente mais tarde.
					<button type="button" class="close" data-dismiss="alert" aria-label="Close">
					<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<?php endif ?>

				<?php if (has_role('becomex_pmo') || has_role('sysadmin')): ?>
				<div class="col-md-9 no-left-padding">
					<div class="form-group">
	                    <select class="form-control selectpicker" name="id_segmento" id="id_segmento" data-live-search="true" data-title="Selecione o segmento">
	                       	<option value="">Selecione o segmento</option>
	                       	<?php if (!empty($segmentos)): ?>
		                       	<?php foreach ($segmentos as $segmento): ?>
	                       		<option value="<?php echo $segmento->id_segmento ?>" 
	                       			<?php 
	                       			echo set_select('id_segmento', $segmento->id_segmento, $this->input->get('id_segmento') == $segmento->id_segmento);
	                       			?>
	                       			>
	                       			<?php echo $segmento->descricao ?>
	                       		</option>
		                       	<?php endforeach ?>
	                       	<?php endif ?>
	                    </select>
	                </div>
				</div>
				<?php endif ?>
				<div class="col-md-3 <?php echo (has_role('becomex_pmo') || has_role('sysadmin')) ? "no-right-padding" : "no-left-padding" ?>">
					<div class="form-group">
							<input class="form-control" type="number" name="num_predicoes" id="num_predicoes" min="0" placeholder="num. de predições" value="<?php echo (isset($_GET['num_predicoes']) ? $_GET['num_predicoes'] : null ) ?>">
					</div>
				</div>

				<div class="<?php echo (has_role('becomex_pmo') || has_role('sysadmin')) ? "col-md-12" : "col-md-9" ?> no-left-padding no-right-padding">
					<div class="input-group">
						<input class="form-control" type="text" name="termo_pesquisa" id="termo_pesquisa" placeholder="Termo de pesquisa" value="<?php echo (isset($_GET['termo_pesquisa']) ? $_GET['termo_pesquisa'] : null ) ?>">

						<?php if ($this->input->get() && !empty($result)): ?>
						<span class="input-group-btn">
							<button data-loading-text="..." type="submit" class="btn btn-primary btn-block search-button"><i class="glyphicon glyphicon-search"></i> </button>
						</span>
						<span class="input-group-btn">
							<button data-loading-text="..." type="button" class="btn btn-primary btn-block export-button"><i class="glyphicon glyphicon-download-alt"></i></button>
						</span>
						<?php else: ?>
						<span class="input-group-btn">
							<button data-loading-text="..." type="submit" class="btn btn-primary btn-block search-button"><i class="glyphicon glyphicon-search"></i> </button>
						</span>						
						<?php endif ?>
					</div>
				</div>
			</div>

			<input type="hidden" name="exportar" value="">
		</form>

		<?php if (!empty($result) && !empty($status_api)): ?>
			<div class="results"> 
				<?php 

				if (!empty($apps))
				{
					foreach ($apps as $app_key => $title) 
					{
						$ellapsed_time = $result[$app_key . '_ellapsed'];
						$this->load->view('consulta_diana/block_search_result', 
							array(
								'title' => $title,
								'ellapsed_time' => $ellapsed_time,
								'app' => $result[$app_key]
							)
						);
					}
				}
				
				?>
				
			</div>
		<?php endif ?>
	</div>
</div>

<style>
	.desc-content{
		margin-bottom:  10px;
	}
</style>

<script>

$(".export-button").on("click", function() {
	submit_form(this, 1);
});

$(".search-button").on("click", function() {
	submit_form(this, 0);
});

var submit_form = function (that, exportar) {
	$(that).button('loading');
	$("input[name=exportar]").val(exportar);
	$("#search-api-form").submit();	
}

$(".ncm_info").on("click", function ()
{
	var self = $(this);
	var ncm = self.attr("data-attr-ncm");
		
	if (self.parent().find('.table-content').html() == '')
	{
		clean_html();
		$.ajax({
			url: '<?php echo site_url() ?>'+'atribuir_grupo/ajax_get_ncm_info',
			data: {'ncm': ncm},
			method: 'POST',
			success: function(data)
			{
				self.parent().find('.table-content').html(data + '<hr>');
			},
			beforeSend: function()
			{
				loading("open", ncm);
			},
			complete: function ()
			{
				loading("close", ncm);
			}
		})
	}else
	{
		self.parent().find('.table-content').html('');	
	}
});

function clean_html()
{
	$('.table-content, .desc-content').html('');
}

$(".info_gt").on("click", function ()
{
	var self = $(this);
	var id_grupo_tarifario = self.attr("data-attr-id-grupo-tarifario");
	var ncm = self.attr("data-attr-ncm");

	var html = "";

	if (self.parent().find('.desc-content').html() == '')
	{
		clean_html();
		
		$.ajax({
			url: '<?php echo site_url() ?>'+'consulta_diana/ajax_get_info_grupo_tarifario',
			data: {'id_grupo_tarifario': id_grupo_tarifario},
			method: 'POST',
			success: function(data)
			{

				data = JSON.parse(data);

				if(data.observacao){
					html += "<b>Observacao</b>: " + data.observacao;
				}

				if(data.observacao && data.subsidio)
				{
					html += "<br><br>";
				}

				if(data.subsidio)
				{
					html += "<b>Subsídio</b>: " + data.subsidio
				}

				if(data.observacao || data.subsidio)
				{
					html += "<hr>";
				}

				if (html == '')
				{
					html = 'Não foi encontrada nenhuma informação.';
				}

				self.parent().find('.desc-content').html(html);
			},
			beforeSend: function()
			{
				loading("open", ncm);
			},
			complete: function ()
			{
				loading("close", ncm);
			}
		})
	}else
	{
		self.parent().find('.desc-content').html('');
	}
});

function loading(action, ncm)
{
	var loading = $(".loading-info-"+ ncm);
	
	if(action == "open")
	{
		loading.css("display", "inherit");
	} else 
	{
		loading.css('display', 'none');
	}
}


</script>