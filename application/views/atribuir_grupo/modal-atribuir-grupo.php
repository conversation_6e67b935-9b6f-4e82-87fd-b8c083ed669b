<div class="modal fade modal-lg" id="modal_motivo" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" aria-hidden="true">
    <div class="modal-dialog" style="width: 80%">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <div class="text-top">
                <h4 class="modal-title">Atribuir Grupo</h4> <img class="loading" src="<?php echo base_url('assets/img/loading_ajax.gif') ?>" width="20" height="20">
                </div>
            </div>
            <div class="modal-body">
 
                <ul class="nav nav-tabs" role="tablist">
                    <li class="active" role="presentation">
                        <a href="#dados" aria-controls="home" role="tab" data-toggle="tab">Dados básicos</a>
                    </li>

                    <li role="presentation">
                        <a href="#suframa" aria-controls="suframa" role="tab" data-toggle="tab">SUFRAMA</a>
                    </li>

                    <li role="presentation">
                        <a href="#aba-nve" aria-controls="aba-nve" role="tab" data-toggle="tab">NVE</a>
                    </li>

                    <li role="presentation">
                        <a href="#aba-li" aria-controls="aba-li" role="tab" data-toggle="tab">LI</a>
                    </li>

                    <li role="presentation">
                        <a href="#aba-ex-ii" aria-controls="aba-ex-ii" role="tab" data-toggle="tab">EX II</a>
                    </li>

                    <li role="presentation">
                        <a href="#aba-ex-ipi" aria-controls="aba-ex-ipi" role="tab" data-toggle="tab">EX IPI</a>
                    </li>

                    <li role="presentation">
                        <a href="#aba-antidumping" aria-controls="aba-antidumping" role="tab" data-toggle="tab">ANTIDUMPING</a>
                    </li>

                    <li role="presentation">
                        <a href="#aba-energetica" aria-controls="aba-energetica" role="tab" data-toggle="tab">CLASSIFICAÇÃO ENERGÉTICA</a>
                    </li>

                    <li role="presentation">
                        <a href="#aba-atributos" aria-controls="aba-atributos" role="tab" data-toggle="tab">ATRIBUTOS</a>
                    </li>
                    <li role="presentation">
                        <a href="#aba-perguntas-respostas" aria-controls="aba-perguntas-respostas" role="tab" data-toggle="tab">Perguntas e Respostas</a>
                    </li>
                </ul>

                <div class="tab-content">
                    <?php $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_dados_basicos') ?>
                    <?php $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_suframa') ?>
                    <?php $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_nve') ?>
                    <?php $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_li') ?>
                    <?php $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_ex_ii') ?>
                    <?php $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_ex_ipi') ?>
                    <?php $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_antidumping') ?>
                    <?php $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_energetica') ?>
                    <?php $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_atributos') ?>
                    <?php $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_perguntas_respostas') ?>
                </div>
            </div>

            <input type="hidden" id="suframa-produto-ncm" value="">
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        var getSuframaProdutoUrl = $base_url + "atribuir_grupo/xhr_get_suframa_produtos_by_code";
        var suframaProdutoNcm = $('#suframa-produto-ncm');
        var suframaProdutoSelect = $('#suframa-produto-select');
        var destaqueTbody = $('.suframa-destaque-tbody');


        suframaProdutoSelect.on('change', function() {
            var codigo = $(this).val();
            var ncm = suframaProdutoNcm.val();

            if (codigo && ncm) {
                getSuframaProduto(codigo, ncm);
            } else {
                showTableWarningMessage();
            }
        });

        function showTableWarningMessage() {
            var message = `<tr>
            <td colspan="999">Selecione um grupo tarifário para associar</td>
        </tr>`;

            $('.suframa-destaque-tbody').html(message);
        }

        function getSuframaProduto(code, ncm) {
            $.ajax({
                url: getSuframaProdutoUrl,
                type: 'get',
                dataType: "json",
                data: {
                    'code': code,
                    'ncm': ncm
                },
                success: function(response) {
                    if (response.data) {
                        populateDestaque(response.data, ncm);
                    }
                },
                error: function() {}
            })
        }

        function populateDestaque(itens, ncm) {
            destaqueTbody.html('');
            var elements = '';

            if (!itens || itens.length == 0) {
                elements = `<tr>
                <td colspan="999">Nenhum destaque foi encontrado para a ncm ${ncm}</td>
            </tr>`;
            } else {

                $.each(itens, function(index, item) {

                    elements += `<tr>
                    <td><input type="radio" name="destaque" value="${item.DESTAQUE}"></td>
                    <td>${item.DESTAQUE}</td>
                    <td class="suframa-ppb">${item.PPB}</td>
                    <td class="suframa-codigo">${item.CODIGO}</td>
                    <td class="suframa-descricao">${item.DESCRICAO_SUFRAMA}</td>
                    <td class="suframa-produto hide">${item.PRODUTO}</td>
                </tr>`;

                })
            }


            destaqueTbody.append(elements);
        }


    })
</script>

<style>
    .text-top h4{
        display: inline-block;
        vertical-align: top;
    }    
</style>