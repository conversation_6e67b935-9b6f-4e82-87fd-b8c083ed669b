<style>
    .bulk_select_all.disabled>a {
        text-decoration: none;
        color: #aaa
    }

    .btn-filter {
        font-size: 13px;
    }
</style>

<script>
    $(document).ready(function() {
        $('#collapseOne').collapse('hide');

        const campos = [{
                id: '#integracao',
                tipo: 'select'
            },
            {
                id: '#integracao_ecomex',
                tipo: 'select'
            },
            {
                id: '#pendentes',
                tipo: 'select'
            },
            {
                id: '#status_atributos',
                tipo: 'select'
            },
            {
                id: '#status_preenchimento',
                tipo: 'select'
            },
            {
                id: '#estabelecimento_modal',
                tipo: 'select'
            },
            {
                id: '#data_inicio_homologacao_modal',
                tipo: 'text'
            },
            {
                id: '#data_fim_homologacao_modal',
                tipo: 'text'
            }
        ];

        const temValor = campos.some(campo => {
            if (!$(campo.id).length) return false; // ignora campos inexistentes

            const val = $(campo.id).val();

            if (campo.tipo === 'select') {
                if (Array.isArray(val)) {
                    return val.length > 0 && val.some(v => v !== '-1' && v !== '');
                }
                return val && val !== '-1' && val !== '';
            }

            return val && val.trim() !== '';
        });

        if (temValor) {
            $('#collapseOne').collapse('show');
        }
    });
</script>

<div class="page-header">
    <div id="ajax_validate"></div>
    <h2>
        Homologação
        <?php if (count($itens) && in_array('exportar', $funcoes_adicionais)) : ?>

            <?php if ($empresa_pais) : ?>
                <div class="dropdown pull-right" style="top: -2px;">
                    <button class="btn btn-default dropdown-toggle" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true" data-loading-text="Gerando...">
                        Exportar <i class="glyphicon glyphicon-cloud-download"></i>
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">
                        <li><a href="#" class="btn-download-xls" data-loading-text="Gerando...">Padrão</a></li>
                        <li><a href="#" class="btn-paises-xls" data-loading-text="Gerando...">Multi Paises</a></li>
                        <?php if (count($itens) && in_array('exportar_diana', $funcoes_adicionais)) : ?>
                            <li><a href="#" class="btn-gerar-xls" data-loading-text="Gerando..."> Gerar Planilha Atributos</a></li>
                        <?php endif; ?>
                    </ul>
                </div>
            <?php else : ?>
                <?php if (count($itens) && in_array('exportar_diana', $funcoes_adicionais)) : ?>
                    <button data-loading-text="Gerando..." class="btn btn-default btn-gerar-xls pull-right">
                        Gerar Planilha Atributos <i class="glyphicon glyphicon-cloud-download"></i>
                    </button>
                <?php endif; ?>

                <button data-loading-text="Gerando..." class="btn btn-default btn-download-xls pull-right" style="margin-right:5px;">
                    Exportar <i class="glyphicon glyphicon-cloud-download"></i>
                </button>
            <?php endif; ?>
        <?php endif; ?>

        <?php if (count($itens) && in_array('exportar_lista_impacto', $funcoes_adicionais)) : ?>
            <a href="#" id="btn-lista_impacto-xls" class="btn btn-default pull-right " style="margin-right:5px;">
                Exportar Lista de Impacto <i class="glyphicon glyphicon-cloud-download"></i>
            </a>
        <?php endif; ?>

        <?php if (count($itens) && in_array('planilha_upload', $funcoes_adicionais)) : ?>
            <button data-loading-text="Gerando..." class="btn btn-default btn-download-xls-upload pull-right" style="margin-right:5px;">
                Exportar Planilha de Upload <i class="glyphicon glyphicon-cloud-download"></i>
            </button>
        <?php endif; ?>
    </h2>
</div>

<?php
echo form_open('', array('id' => 'form_homologacao', 'method' => 'POST'));

$list_opt = $this->cad_item_model->get_state('filter.list_opt');

$homologar = $homologado = $homologado_em_revisao = $nao_homologado = $obsoleto = $revisao = $todos = FALSE;

$atribuido_para = $this->cad_item_model->get_state('filter.atribuido_para');

switch ($list_opt) {
    case 'homologado':
        $homologado = TRUE;
        break;

    case 'homologado_em_revisao':
        $homologado_em_revisao = TRUE;
        break;

    case 'nao_homologado':
        $nao_homologado = TRUE;
        break;

    case 'obsoleto':
        $obsoleto = TRUE;
        break;

    case 'revisao':
        $revisao = TRUE;
        break;

    case 'todos':
        $todos = TRUE;
        break;

    default:
        $homologar = TRUE;
}

ob_start();
?>
<strong><em>Quando:</em></strong>
<ul>
    <li>
        <strong>Meu usuário</strong>: Pendências de aprovação para o meu usuário.
    </li>
    <li><strong>Todos os responsáveis</strong>: Pendências de aprovação de todos os usuários.</li>
    <li><strong>Algum usuário específico</strong>: Pendências de aprovação para o usuário escolhido.</li>
</ul>
<?php
$title_text_1 = ob_get_contents();
ob_end_clean();

//
ob_start();
?>
<strong><em>Quando:</em></strong>
<ul>
    <li>
        <strong>Meu usuário</strong>: Pendências de revisão para o meu usuário.
    </li>
    <li><strong>Todos os responsáveis</strong>: Pendências de revisão de todos os usuários.</li>
    <li><strong>Algum usuário específico</strong>: Pendências de revisão para o usuário escolhido.</li>
</ul>
<?php
$title_text_6 = ob_get_contents();
ob_end_clean();
//

//
ob_start();
?>
<!-- Texto a ser exibido no botão do novo status Homologado em revisão. -->
<strong><em>Quando:</em></strong>
<ul>
    <li>
        <strong>Meu usuário</strong>: Itens sob minha responsabilidade que já foram aprovados mas sofreram alteração posteriormente e precisa ser revisado,
    </li>
    <li><strong>Todos os responsáveis</strong>: Itens aprovados por qualquer usuário mas que sofreram alteração posteriormente e precisa ser revisado.</li>
    <li><strong>Algum usuário específico</strong>: Itens aprovados pelo usuário escolhido mas que sofreu alteração e precisa ser revisado.</li>
</ul>
<?php
$title_text_7 = ob_get_contents();
ob_end_clean();
//

//
ob_start();
?>

<strong><em>Quando:</em></strong>
<ul>
    <li>
        <strong>Meu usuário</strong>: Itens sob minha responsabilidade, que já foram aprovados
    </li>
    <li><strong>Todos os responsáveis</strong>: Itens já aprovados por qualquer usuário.</li>
    <li><strong>Algum usuário específico</strong>: Itens já aprovados pelo usuário escolhido.</li>
</ul>
<?php
$title_text_2 = ob_get_contents();
ob_end_clean();

//
ob_start();
?>
<strong><em>Quando:</em></strong>
<ul>
    <li>
        <strong>Meu usuário</strong>: Itens sob minha responsabilidade, que foram reprovados.
    </li>
    <li><strong>Todos os responsáveis</strong>: Itens reprovados por qualquer usuário.</li>
    <li><strong>Algum usuário específico</strong>: Itens reprovados pelo usuário escolhido.</li>
</ul>
<?php
$title_text_3 = ob_get_contents();
ob_end_clean();

//
ob_start();
?>
<strong><em>Quando:</em></strong>
<ul>
    <li>
        <strong>Meu usuário</strong>: Itens sob minha responsabilidade, que foram definidos como Inativo.
    </li>
    <li><strong>Todos os responsáveis</strong>: Itens definidos como Inativo por qualquer usuário.</li>
    <li><strong>Algum usuário específico</strong>: Itens definidos como Inativo pelo usuário escolhido.</li>
</ul>
<?php
$title_text_4 = ob_get_contents();
ob_end_clean();

//
ob_start();
?>
<strong><em>Quando:</em></strong>
<ul>
    <li>
        <strong>Meu usuário</strong>: Lista todos os itens sob minha responsabilidade.
    </li>
    <li><strong>Todos os responsáveis</strong>: Lista todos os itens.</li>
    <li><strong>Algum usuário específico</strong>: Lista todos os itens sob responsabilidade do usuário escolhido.</li>
</ul>
<?php if (has_role('sysadmin')) { ?>
    <strong>Atenção:</strong> esta listagem desconsidera os itens em revisão.
<?php } ?>
<?php
$title_text_5 = ob_get_contents();
ob_end_clean();

?>

<style type="text/css" media="screen">
    body>.tooltip .tooltip-inner {
        text-align: left;
        width: 300px;
        max-width: 300px;
    }

    @media (max-width: 991px) {
        #filter-1 {
            margin-bottom: 10px;
        }
    }

    #select-grupo-tarifario+.bootstrap-select span.text {
        width: 700px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    #select-grupo-tarifario+.bootstrap-select .dropdown-menu.open {
        max-width: 800px;
    }

    .result-container {
        flex-grow: 1;
    }

    .result-container .col-md-8 {
        max-width: 100%;
    }

    button.disabled {
        pointer-events: none;
    }

    @media (min-width: 992px) {
        .ff .col-md-3.col-lg-3 {
            margin: 6px 0;
        }
    }
</style>
<div class="row ff">

    <?php
    $class = 'col-md-4 col-lg-4';
    if (in_array('integracao_ecomex', $funcoes_adicionais)) {
        $classAdvancedFilters = 'col-md-3 col-lg-3';
    } else {
        $classAdvancedFilters = 'col-md-4 col-lg-4';
    }
    ?>
    <div id="filter-2" class="<?php echo $class; ?>">
        <div class="row">
            <div class="col-md-12">
                <strong>Atribuídos para: </strong>
            </div>

            <div class="col-md-12 form-group">
                <select class="form-control" style="width: 100%" name="atribuido_para" id="atribuido_para">
                    <option <?php echo set_select('atribuido_para', sess_user_id(), sess_user_id() == $this->cad_item_model->get_state('filter.atribuido_para')) ?> value="<?php echo sess_user_id() ?>">Meu usuário</option>
                    <option <?php echo set_select('atribuido_para', -1, -1 == $this->cad_item_model->get_state('filter.atribuido_para')) ?> value="-1">Todos</option>
                    <?php

                    foreach ($lista_usuarios as $usuario) {
                        if ($usuario->id_usuario == sess_user_id()) continue;
                    ?>
                        <option <?php echo set_select('atribuido_para', $usuario->id_usuario, $usuario->id_usuario == $this->cad_item_model->get_state('filter.atribuido_para')) ?> value="<?php echo $usuario->id_usuario ?>"><?php echo $usuario->nome . ' - ' . $usuario->perfil ?></option>
                    <?php
                    }

                    ?>
                </select>
            </div>
        </div>
    </div>

    <div id="filter-4" class="<?php echo $class; ?>">
        <div class="row">
            <div class="col-md-12">
                <strong>Evento/Pacote: </strong>
            </div>

            <div class="col-md-12 form-group">
                <select
                    class="form-control selectpicker"
                    multiple
                    data-selected-text-format="count > 1"
                    data-count-selected-text="Eventos ({0})"
                    name="evento[]"
                    title="Todos"
                    data-actions-box="true"
                    data-select-all-text="Todos"
                    data-deselect-all-text="Nenhum"
                    data-live-search="true"
                    style="width: 100%"
                    id="evento">
                    <option value="">Todos</option>
                    <option <?php echo !empty($this->input->post('evento')) && in_array('sem_evento', $this->input->post('evento')) ? 'selected' : '' ?> value="sem_evento"><strong>Sem Evento/Pacote</strong></option>

                </select>
            </div>
        </div>
    </div>

    <?php if ($has_status_exportacao) :
        $status_exp_selected = $this->cad_item_model->get_state('filter.status_exportacao') ?>
        <div id="filter-5" class="<?php echo $class; ?>">
            <div class="row">
                <div class="col-md-12">
                    <strong>Status Exportação: </strong>
                </div>

                <div class="col-md-12 form-group">
                    <select class="form-control selectpicker" multiple="multiple" data-selected-text-format="count > 0" data-count-selected-text="Status Exportação ({0})" name="status_exportacao[]" title="Nenhum" data-actions-box="true" data-select-all-text="Todos" data-deselect-all-text="Nenhum" id="status_exportacao">
                        <option value="0" <?php echo is_array($status_exp_selected) && in_array(0, $status_exp_selected) ? 'selected' : ''; ?>>Pendente</option>
                        <option value="1" <?php echo is_array($status_exp_selected) && in_array(1, $status_exp_selected) ? 'selected' : ''; ?>>Exportado</option>
                    </select>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if ($simplus) : ?>
        <div id="filter-2" class="<?php echo $class; ?>">
            <div class="row">
                <div class="col-md-12">
                    <strong>Status SimplusTEC: </strong>
                </div>

                <div class="col-md-12 form-group">
                    <select class="form-control selectpicker" multiple="multiple" data-selected-text-format="count > 0" data-count-selected-text="Status ({0})" name="status_simplus[]" id="status_simplus">
                        <option value="0" <?php echo in_array(0, $status_simplus) ? 'selected' : ''; ?>>Pendente</option>
                        <option value="1" <?php echo in_array(1, $status_simplus) ? 'selected' : ''; ?>>Enviado</option>
                    </select>
                </div>
            </div>
        </div>
    <?php else :
        $status_imp_selected = $this->cad_item_model->get_state('filter.status_implementacao') ?>
        <div id="filter-2" class="<?php echo $class; ?>">
            <div class="row">
                <div class="col-md-12">
                    <strong>Status Implementação: </strong>
                </div>

                <div class="col-md-12 form-group">
                    <select class="form-control selectpicker" multiple="multiple" data-selected-text-format="count > 0" data-count-selected-text="Status ({0})" name="status_implementacao[]" title="Nenhum" data-actions-box="true" data-select-all-text="Todos" data-deselect-all-text="Nenhum" id="status_implementacao">
                        <option value="I" <?php echo is_array($status_imp_selected) && in_array('I', $status_imp_selected) ? 'selected' : ''; ?>>Implementado</option>
                        <option value="N" <?php echo is_array($status_imp_selected) && in_array('N', $status_imp_selected) ? 'selected' : ''; ?>>Não implementado</option>
                        <option value="R" <?php echo is_array($status_imp_selected) && in_array('R', $status_imp_selected) ? 'selected' : ''; ?>>Revisão</option>
                    </select>
                </div>
            </div>
        </div>
    <?php endif ?>
    <?php if (in_array('prioridade', $campos_adicionais)) : ?>
        <div id="filter-prioridade" class="<?php echo $class; ?>">
            <div class="row">
                <div class="col-md-12">
                    <strong>Prioridade: </strong>
                </div>

                <div class="col-md-12 form-group">

                    <select
                        class="form-control selectpicker"
                        multiple
                        data-selected-text-format="count > 1"
                        data-count-selected-text="Prioridade ({0})"
                        name="prioridade[]"
                        title="Todos"
                        data-actions-box="true"
                        data-select-all-text="Todos"
                        data-deselect-all-text="Nenhum"
                        data-live-search="true"
                        style="width: 100%"
                        id="prioridade">

                    </select>
                </div>
            </div>
        </div>
    <?php endif; ?>
    <?php if (in_array('owner', $campos_adicionais)) : ?>
        <div id="filter-7" class="<?php echo $class; ?>">
            <div class="row">
                <div class="col-md-12">
                    <strong>Owner: </strong>
                </div>
                <div class="col-md-12 form-group">
                    <select
                        class="selectpicker form-control"
                        style="width: 100%"
                        name="owner[]"
                        id="owner"
                        data-live-search="true"
                        multiple
                        data-selected-text-format="count > 1"
                        data-count-selected-text="Owner ({0})"
                        title="Selecione">
                        <option data-divider="true"></option>
                    </select>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>
<div class="row">
    <div class="col-sm-12">
        <textarea name="part_numbers" type="text" class="form-control" placeholder="Informe o código do produto aqui" style="margin: 10px 0; height: 50px; width: 100%"><?php echo $this->cad_item_model->get_state('filter.part_numbers_view') ? $this->cad_item_model->get_state('filter.part_numbers_view') : "" ?></textarea>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="panel-group" id="accordionx" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading" role="tab" id="headingOne">
                    <h4 class="panel-title">
                        <a role="button" data-toggle="collapse" data-parent="#accordionx" href="#collapseOne" aria-expanded="false" aria-controls="collapseOne">
                            <span class="glyphicon glyphicon-filter" aria-hidden="true"></span> Filtros Avançados
                        </a>
                    </h4>
                </div>
                <div id="collapseOne" class="panel-collapse collapse" role="tabpanel" aria-labelledby="headingOne" aria-expanded="false">
                    <div class="panel-body">
                        <?php if (in_array('owner', $campos_adicionais)) : ?>
                            <div id="filter-7" class="<?php echo $classAdvancedFilters; ?>">
                                <div class="row">
                                    <div class="col-md-12">
                                        <strong>Integração: </strong>
                                    </div>
                                    <div class="col-md-12 form-group">
                                        <select class="selectpicker form-control" style="width: 100%" name="integracao" id="integracao" data-live-search="true">
                                            <option <?php echo set_select('integracao', -1, -1 == $this->cad_item_model->get_state('filter.integracao')) ?> value="-1">Todos</option>
                                            <!-- <option value="-1">Todos</option> -->
                                            <option data-divider="true"></option>
                                            <option <?php echo set_select('integracao', 10, 10 == $this->cad_item_model->get_state('filter.integracao')) ?> value="10">Pendente de integração</option>
                                            <option <?php echo set_select('integracao', 1001, 1001 == $this->cad_item_model->get_state('filter.integracao')) ?> value="1001">Integrado</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if (in_array('integracao_ecomex', $funcoes_adicionais)) : ?>
                            <div id="filter-7" class="<?php echo $classAdvancedFilters; ?>">
                                <div class="row">
                                    <div class="col-md-12">
                                        <strong>Integração eComex: </strong>
                                    </div>
                                    <div class="col-md-12 form-group">
                                        <select class="selectpicker form-control" style="width: 100%" name="integracao_ecomex" id="integracao_ecomex">
                                            <option <?php echo set_select('integracao_ecomex', -1, -1 == $this->item_model->get_state('filter.integracao_ecomex')) ?> value="-1">Todos</option>
                                            <option data-divider="true"></option>
                                            <option <?php echo set_select('integracao_ecomex', 10, 10 == $this->item_model->get_state('filter.integracao_ecomex')) ?> value="10">Nacional</option>
                                            <option <?php echo set_select('integracao_ecomex', 1001, 1001 == $this->item_model->get_state('filter.integracao_ecomex')) ?> value="1001">Importado</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div id="filter-7" class="<?php echo $classAdvancedFilters; ?>">
                                <div class="row">
                                    <div class="col-md-12">
                                        <strong>Atribuições Pendentes: </strong>
                                    </div>
                                    <div class="col-md-12 form-group">
                                        <select class="selectpicker form-control" style="width: 100%" name="pendentes" id="pendentes">
                                            <option <?php echo set_select('pendentes', -1, -1 == $this->cad_item_model->get_state('filter.pendentes')) ?> value="-1">Todos</option>
                                            <option data-divider="true"></option>
                                            <option <?php echo set_select('pendentes', 'pendente_atributos', 'pendente_atributos' == $this->cad_item_model->get_state('filter.pendentes')) ?> value="pendente_atributos">Atributos</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                        <div id="filter-sistema-origem" class="<?php echo $classAdvancedFilters; ?>">
                            <div class="row">
                                <div class="col-md-12">
                                    <strong>Sistema de Origem: </strong>
                                </div>

                                <div class="col-md-12 form-group">
                                    <select
                                        class="form-control selectpicker"
                                        multiple
                                        data-selected-text-format="count > 1"
                                        data-count-selected-text="Sistema de Origem ({0})"
                                        name="sistema_origem[]"
                                        title="Todos"
                                        data-actions-box="true"
                                        data-select-all-text="Todos"
                                        data-deselect-all-text="Nenhum"
                                        data-live-search="true"
                                        style="width: 100%"
                                        id="sistema_origem">
                                        <option data-divider="true"></option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div id="filter-status-atributos" class="<?php echo $classAdvancedFilters; ?>">
                            <div class="row">
                                <div class="col-md-12">
                                    <strong>Status Atributos: </strong>
                                </div>

                                <div class="col-md-12 form-group">
                                    <select
                                        class="form-control selectpicker"
                                        multiple
                                        data-selected-text-format="count > 1"
                                        data-count-selected-text="Status de atributos ({0})"
                                        name="status_atributos[]"
                                        title="Todos"
                                        data-actions-box="true"
                                        data-select-all-text="Todos"
                                        data-deselect-all-text="Nenhum"
                                        data-live-search="true"
                                        style="width: 100%"
                                        id="status_atributos">
                                        <?php $status_atributossel = $this->cad_item_model->get_state('filter.status_atributos') ?: []; ?>

                                        <option <?php echo empty($status_atributossel) ? 'selected' : '' ?> value="">Todos</option>

                                        <?php foreach ($status_todos_atributos as $status_atributos) : if ($status_atributos->status == 'Item nacional') continue; ?>

                                            <option <?php echo in_array($status_atributos->id, $status_atributossel) ? 'selected' : '' ?>
                                                value="<?php echo $status_atributos->id ?>">

                                                <?php echo $status_atributos->status ?>

                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div id="filter-status-preenchimento-atributos" class="<?php echo $classAdvancedFilters; ?>">
                            <div class="row">
                                <div class="col-md-12">
                                    <strong>Status Preenchimento Atributos: </strong>
                                </div>

                                <div class="col-md-12 form-group">
                                    <select
                                        class="form-control selectpicker"
                                        multiple
                                        data-selected-text-format="count > 1"
                                        data-count-selected-text="Status de Preenchimento ({0})"
                                        name="status_preenchimento[]"
                                        title="Todos"
                                        data-actions-box="true"
                                        data-select-all-text="Todos"
                                        data-deselect-all-text="Nenhum"
                                        data-live-search="true"
                                        style="width: 100%"
                                        id="status_preenchimento">
                                        <?php $status_preenchimento = (array) $this->cad_item_model->get_state('filter.status_preenchimento') ?: []; ?>

                                        <option <?php echo in_array(1, $status_preenchimento) ? "selected" : "" ?> value="1">
                                            Sem preenchimento
                                        </option>

                                        <option <?php echo in_array(2, $status_preenchimento) ? "selected" : "" ?> value="2">
                                            Atributos obrigatórios não preenchidos
                                        </option>

                                        <option <?php echo in_array(3, $status_preenchimento) ? "selected" : "" ?> value="3">
                                            Atributos opcionais não preenchidos
                                        </option>

                                        <option <?php echo in_array(4, $status_preenchimento) ? "selected" : "" ?> value="4">
                                            Totalmente preenchidos
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div id="filter-estabelecimento" class="<?php echo $classAdvancedFilters; ?>">
                            <div class="row">
                                <div class="col-md-12">
                                    <strong>Estabelecimento: </strong>
                                </div>
                                <div class="col-md-12 form-group">
                                    <?php $estabelecimento_selected = $this->cad_item_model->get_state('filter.estabelecimento_modal') ?>
                                    <select class="selectpicker form-control" style="width: 100%" name="estabelecimento_modal[]" id="estabelecimento_modal" data-live-search="true" multiple data-selected-text-format="count > 1" data-count-selected-text="Estabelecimento ({0})" title="Selecione o estabelecimento">
                                        <option <?php echo empty($estabelecimento_selected) ? 'selected' : '' ?> value="-1">Todos os estabelecimentos</option>
                                        <?php foreach ($estabelecimentos as $estabelecimento) : ?>
                                            <?php if ($this->input->post('estabelecimento_modal') !== false) :
                                                $selected = is_array($estabelecimento_selected) ? in_array($estabelecimento, $estabelecimento_selected) : $estabelecimento == $estabelecimento_selected; ?>
                                                <option value="<?php echo $estabelecimento ?>" <?php echo $selected ? 'selected' : null; ?>>
                                                    <?php echo $estabelecimento; ?>
                                                </option>
                                            <?php else :
                                                $selected = false;
                                                $selected_values = $this->input->get('estabelecimento_modal');
                                                if (!empty($selected_values)) {
                                                    foreach ($selected_values as $selected_value) {
                                                        if ($selected_value === $estabelecimento) {
                                                            $selected = true;
                                                            break;
                                                        }
                                                    }
                                                }
                                            ?>
                                                <option <?php echo $selected ? 'selected' : ''; ?> value="<?php echo $estabelecimento; ?>">
                                                    <?php echo $estabelecimento; ?>
                                                </option>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div id="filter-ncm-proposto" class="<?php echo $classAdvancedFilters; ?>">
                            <div class="row">
                                <div class="col-md-12">
                                    <strong>NCM Proposto: </strong>
                                </div>
                                <div class="col-md-12 form-group">
                                    <select
                                        class="selectpicker form-control"
                                        style="width: 100%"
                                        name="ncm_proposta_modal[]"
                                        id="ncm_proposta_modal"
                                        data-live-search="true"
                                        multiple
                                        data-selected-text-format="count > 1"
                                        data-count-selected-text="NCM Proposto({0})"
                                        title="Selecione o NCM">
                                        <option value="-1">Todos os ncms propostos</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div id="filter-data-inicio-homologacao" class="<?php echo $classAdvancedFilters; ?>">
                            <div class="row">
                                <div class="col-md-12">
                                    <label for="data_inicio_homologacao_modal">Data Inicial Homologação:</label>
                                    <div class="form-group" style="width: 100%">
                                        <div class="input-group date datetimepicker" style="width: 100%" id="datepicker_ini_homologacao_modal">
                                            <?php if ($this->input->post('data_inicio_homologacao_modal') !== false) : ?>
                                                <input
                                                    type="text"
                                                    class="form-control datetimepicker"
                                                    name="data_inicio_homologacao_modal"
                                                    id="data_inicio_homologacao_modal"
                                                    value="<?php echo $this->input->post('data_inicio_homologacao_modal') ?>"
                                                    placeholder="" />
                                            <?php else : ?>
                                                <input
                                                    type="text"
                                                    class="form-control datetimepicker"
                                                    name="data_inicio_homologacao_modal"
                                                    id="data_inicio_homologacao_modal"
                                                    value="<?php echo $this->cad_item_model->get_state('filter.data_inicio_homologacao_modal') ?>"
                                                    placeholder="" />
                                            <?php endif; ?>
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="filter-data-fim-homologacao" class="<?php echo $classAdvancedFilters; ?>">
                            <div class="row">
                                <div class="col-md-12">
                                    <label for="data_fim_homologacao_modal">Data Final Homologação:</label>
                                    <div class="form-group" style="width: 100%">
                                        <div class="input-group date datetimepicker" style="width: 100%" id="datepicker_fim_homologacao_modal">
                                            <?php if ($this->input->post('data_fim_homologacao_modal') !== false) : ?>
                                                <input
                                                    type="text"
                                                    class="form-control datetimepicker"
                                                    name="data_fim_homologacao_modal"
                                                    id="data_fim_homologacao_modal"
                                                    value="<?php echo $this->input->post('data_fim_homologacao_modal') ?>"
                                                    placeholder="" />
                                            <?php else : ?>
                                                <input
                                                    type="text"
                                                    class="form-control datetimepicker"
                                                    name="data_fim_homologacao_modal"
                                                    id="data_fim_homologacao_modal"
                                                    value="<?php echo $this->cad_item_model->get_state('filter.data_fim_homologacao_modal') ?>"
                                                    placeholder="" />
                                            <?php endif; ?>
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div id="filter-data-inicio-importado" class="<?php echo $classAdvancedFilters; ?>">
                            <div class="row">
                                <div class="col-md-12">
                                    <label for="data_inicio_importado_modal">Data Inicial Tornou-se Importado:</label>
                                    <div class="form-group" style="width: 100%">
                                        <div class="input-group date datetimepicker" style="width: 100%" id="datepicker_ini_importado_modal">
                                            <?php if ($this->input->post('data_inicio_importado_modal') !== false) : ?>
                                                <input
                                                    type="text"
                                                    class="form-control datetimepicker"
                                                    name="data_inicio_importado_modal"
                                                    id="data_inicio_importado_modal"
                                                    value="<?php echo $this->input->post('data_inicio_importado_modal') ?>"
                                                    placeholder="" />
                                            <?php else : ?>
                                                <input
                                                    type="text"
                                                    class="form-control datetimepicker"
                                                    name="data_inicio_importado_modal"
                                                    id="data_inicio_importado_modal"
                                                    value="<?php echo $this->cad_item_model->get_state('filter.data_inicio_importado_modal') ?>"
                                                    placeholder="" />
                                            <?php endif; ?>
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="filter-data-fim-importado" class="<?php echo $classAdvancedFilters; ?>">
                            <div class="row">
                                <div class="col-md-12">
                                    <label for="data_fim_importado_modal">Data Final Tornou-se Importado:</label>
                                    <div class="form-group" style="width: 100%">
                                        <div class="input-group date datetimepicker" style="width: 100%" id="datepicker_fim_importado_modal">
                                            <?php if ($this->input->post('data_fim_importado_modal') !== false) : ?>
                                                <input
                                                    type="text"
                                                    class="form-control datetimepicker"
                                                    name="data_fim_importado_modal"
                                                    id="data_fim_importado_modal"
                                                    value="<?php echo $this->input->post('data_fim_importado_modal') ?>"
                                                    placeholder="" />
                                            <?php else : ?>
                                                <input
                                                    type="text"
                                                    class="form-control datetimepicker"
                                                    name="data_fim_importado_modal"
                                                    id="data_fim_importado_modal"
                                                    value="<?php echo $this->cad_item_model->get_state('filter.data_fim_importado_modal') ?>"
                                                    placeholder="" />
                                            <?php endif; ?>
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row" style="margin-top: 15px;">
    <div id="filter-1" class="col-lg-7 col-md-8">
        <div class="row">

            <div class="list-group-opt btn-group col-md-12" data-toggle="buttons">
                <label data-html="true" title="<?php echo $title_text_1 ?>" class="btn btn-default btn-filter <?php echo $homologar  ? 'active' : '' ?>">
                    <input type="radio" checked name="list_opt" value="homologar" <?php echo $homologar  ? 'checked="checked"' : '' ?> id="option1"> <i class="glyphicon glyphicon-time"></i> Pend Hom
                </label>
                <?php if (in_array('owner', $campos_adicionais)) : ?>
                    <label data-html="true" title="<?php echo $title_text_7 ?>" class="btn btn-default btn-filter <?php echo $homologado_em_revisao ? 'active' : '' ?>">
                        <input type="radio" name="list_opt" value="homologado_em_revisao" <?php echo $homologado_em_revisao  ? 'checked="checked"' : '' ?> id="option7"> <i class="glyphicon glyphicon-thumbs-up"></i> Hom Rev
                    </label>
                <?php endif; ?>
                <label data-html="true" title="<?php echo $title_text_2 ?>" class="btn btn-default btn-filter <?php echo $homologado ? 'active' : '' ?>">
                    <input type="radio" name="list_opt" value="homologado" <?php echo $homologado  ? 'checked="checked"' : '' ?> id="option2"> <i class="glyphicon glyphicon-thumbs-up"></i> Hom
                </label>
                <label data-html="true" title="<?php echo $title_text_3 ?>" class="btn btn-default btn-filter <?php echo $nao_homologado ? 'active' : '' ?>">
                    <input type="radio" name="list_opt" value="nao_homologado" <?php echo $nao_homologado ? 'checked="checked"' : '' ?> id="option2"> <i class="glyphicon glyphicon-thumbs-down"></i> Reprov
                </label>
                <label data-html="true" title="<?php echo $title_text_4 ?>" class="btn btn-default btn-filter <?php echo $obsoleto ? 'active' : '' ?>">
                    <input type="radio" name="list_opt" value="obsoleto" <?php echo $obsoleto ? 'checked="checked"' : '' ?> id="option3"> <i class="glyphicon glyphicon-asterisk"></i> Inativos
                </label>
                <?php if (has_role('admin') || has_role('consultor')) : ?>
                    <label data-html="true" title="<?php echo $title_text_6 ?>" class="btn btn-default btn-filter <?php echo $revisao ? 'active' : '' ?>">
                        <input type="radio" name="list_opt" value="revisao" <?php echo $revisao ? 'checked="checked"' : '' ?> id="option6"> <i class="glyphicon glyphicon-flag"></i> Revisão
                    </label>
                <?php endif; ?>
                <label data-html="true" title="<?php echo $title_text_5 ?>" class="btn btn-default btn-filter <?php echo $todos ? 'active' : '' ?>">
                    <input type="radio" name="list_opt" value="todos" <?php echo $todos  ? 'checked="checked"' : '' ?> id="option4"> <i class="glyphicon glyphicon-list"></i> Todos
                </label>
            </div>
        </div>
    </div>
    <div class="col-sm-5" style="margin-left: -55px; font-size: 13px;">
        <div class="checkbox" style="margin-right: 2px; margin-top:5px;">
            <label for="ckbx_busca_part_number">
                <input id="ckbx_busca_part_number" rel="busca_part_number" name="ckbx_busca_part_number" type="checkbox" value="1" <?php echo set_checkbox('busca_part_number', $this->cad_item_model->get_state('filter.busca_part_number'), 1) ?> />
                <strong>Part Number</strong>
            </label>
        </div>

        <div class="checkbox" style="margin-right: 2px; margin-top:5px;">
            <label for="ckbx_busca_descricao">
                <input id="ckbx_busca_descricao" rel="busca_descricao" name="ckbx_busca_descricao" type="checkbox" value="1" <?php echo set_checkbox('busca_descricao', $this->cad_item_model->get_state('filter.busca_descricao'), 1) ?> />
                <strong>Descrição</strong>
            </label>
        </div>

        <?php if (has_role('sysadmin') || has_role('consultor')) { ?>
            <div class="checkbox" style="margin-left: 2px; margin-top:5px;">
                <label>
                    <input type="checkbox" <?php echo set_checkbox('sql_output', 1, $this->input->post('sql_output') == 1) ?> name="sql_output" value="1" id="sql_output"> Mostrar resultado SQL
                </label>
            </div>
        <?php } ?>

        <input type="hidden" name="busca_part_number" value="<?php echo (int) $this->cad_item_model->get_state('filter.busca_part_number') ?>" />
        <input type="hidden" name="busca_descricao" value="<?php echo (int) $this->cad_item_model->get_state('filter.busca_descricao') ?>" />
        <a href="<?php echo site_url('homologacao?reset_filters=1'); ?>" class="btn btn-link pull-right" style="font-size: 13px; margin-top: 2px;">Limpar pesquisa</a>
    </div>
</div>
<br>
<div class="row">
    <div class="col-sm-12">
        <input type="hidden" name="filtered" value="1" />
        <button class="btn btn-primary btn-block" id="commit" name="commit" value="1" type="submit">Pesquisar</button>
    </div>
</div>

<input type="hidden" name="id_grupo_tarifario" value="<?php echo $id_grupo_tarifario ?>" />

</form>

<?php if ((has_role('sysadmin') || has_role('consultor')) && $this->input->post('sql_output')) { ?>
    <h3>SQL Output:</h3>
    <pre><?php echo sql_format($query_homolog) ?></pre>
<?php } ?>

<?php if (isset($itens)) { ?>
    <hr />

    <?php if (count($itens) > 0) { ?>

        <div class="col-md-12">
            <div class="row" style="display: flex; align-items: center; flex-direction: row-reverse; margin-bottom: 20px">
                <div class="col-md-5 d-flex pull-right">
                    <div class="col-md-6 pull-right">
                        <?php if ($simplus) : ?>
                            <button id="btn-envia-simplus" class="btn btn-success btn-modal-simplus" href="<?php echo site_url("homologacao/modal_simplus") ?>" title="Envia para Simplus" style="margin-right: 10px;"><i class="glyphicon glyphicon-retweet"></i> Enviar para a Simplus</button>
                        <?php else : ?>
                            <?php if ($list_opt <> 'revisao') : ?>
                                <button id="btn-homologar" class="btn btn-primary" data-toggle="modal" data-target="#homologacao-modal" title="Homologue vários itens ao mesmo tempo!"><i class="glyphicon glyphicon-time"></i> Homologar Em Massa</button>
                            <?php endif; ?>
                            <button id="btn-transferir" class="btn btn-success" data-toggle="modal" data-target="#transferencia-modal" title="Transfira o responsável pelos itens selecionados" <?php echo $list_opt <> 'revisao' ? 'style="margin-top: 10px;"' : ''; ?>><i class="glyphicon glyphicon-user"></i> Transferir responsável</button>
                        <?php endif ?>
                    </div>

                    <div class="col-md-6">
                        <a class="btn btn-primary" data-toggle="modal" data-target="#perguntasRespostas" title="Realizar Perguntas"><i class="glyphicon glyphicon-question-sign"></i> Perguntas e Respostas</a>
                        <?php if ($empresa_pais) : ?>
                            <button id="modal-multi-paises-diana" class="btn btn-primary" type="button" data-toggle="modal" data-target="#modal-multi-paises" style="margin-right: 15px;padding: 6px 48px;margin-top: 10px;"><i data-toggle="tooltip" title="Esta função permitirá efetuar a classificação do item para diversos países" class="glyphicon glyphicon-globe"></i> Multi Países</button>
                        <?php endif; ?>
                        <?php if (customer_has_role('desvincular_grupo', sess_user_id())) : ?>
                            <a style="margin-top: 10px; padding: 6px 48px;" id="desvincularGrupo" href="<?php echo site_url('atribuir_grupo/deletar'); ?>" class="btn btn-danger">
                                Desvincular <i class="glyphicon glyphicon-remove"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-md-7 d-flex result-container">
                    <h2 style="margin: auto 0;">
                        Resultado (<?php echo $total_rows ?>)
                    </h2>
                    <div class="col-md-8" style="margin: auto 0;">
                        <select id="select-grupo-tarifario" data-size="15" data-none-results-text="Nenhum resultado encontrado para {0}" class="form-control selectpicker" data-live-search="true">
                            <option value="" selected disabled>Selecione o grupo tarifário</option>
                            <option value="-1" <?php echo set_select('id_grupo_tarifario', -1, -1 == $id_grupo_tarifario); ?>>Todos os grupos tarifários</option>
                            <?php foreach ($grupos_tarifarios as $grupo) { ?>
                                <option value="<?php echo $grupo->id_grupo_tarifario; ?>" <?php echo set_select('id_grupo_tarifario', $grupo->id_grupo_tarifario, $grupo->id_grupo_tarifario == $id_grupo_tarifario); ?>><?php echo !empty($grupo->ncm_recomendada) ? $grupo->ncm_recomendada . ' - ' . $grupo->descricao : $grupo->descricao; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center">
            <?php echo $this->pagination->create_links() ?>
        </div>

        <div class="alert bulk-selection-box text-center alert-warning" role="alert" <?php echo count($bulk_selection) == 0 ? 'style="display: none"' : '' ?>>
            <div class="bulk-selection-loading"></div>

            <div class="bulk-info-actions">
                Você selecionou: <strong><span class="bulk-selection-count"><?php echo count($bulk_selection) ?></span></strong> item(ns) &ndash;

                <span class="bulk_select_all <?php echo ($total_rows > 5000 ? 'disabled' : '') ?>" data-title="Só é possível selecionar todos os itens quando a quantidade não ultrapassar 5000." <?php echo ($bulk_select_all == false) ? 'style="display: none"' : '' ?>>
                    <a href="javascript: void(0)" onclick="bulk_select_all()">Adicionar todos os <strong><?php echo $total_rows ?></strong> itens</a> &#124;
                </span>

                <a href="javascript: void(0)" onclick="bulk_clean()">Deseja limpar a seleção?</a>
            </div>
        </div>
        <table id="table-list" class="table table-striped table-hover" border="0" cellspacing="5" cellpadding="5">
            <thead>
                <tr>
                    <th width="1%">
                        <input type="checkbox" id="chk-select-all" />
                    </th>
                    <th width="13%" style="text-align: center">Código do Produto</th>
                    <?php if ($multi_estabelecimentos == 1) { ?>
                        <th>Estabelecimento</th>
                    <?php } ?>
                    <th>Descrição Atual</th>
                    <?php if (in_array('owner', $campos_adicionais)) : ?>
                        <th>Owner</th>
                    <?php else : ?>
                        <th>Descrição proposta resumida</th>
                    <?php endif; ?>
                    <?php if (in_array('owner', $campos_adicionais)) : ?>
                        <th>Prioridade</th>
                    <?php else : ?>
                        <th>Evento/Pacote</th>
                    <?php endif; ?>
                    <th>NCM Atual</th>
                    <th>NCM Proposto</th>
                    <th class="text-center">NCM Divergente</th>
                    <th class="text-center">Criação<br />Modificação</th>
                    <th class="text-center">Homologação</th>
                </tr>
            </thead>
            <tbody>
                <?php

                $tipo_homologacao = '';

                if (has_role('engenheiro')) {
                    $tipo_homologacao = 'Engenharia';
                } else if (has_role('fiscal')) {
                    $tipo_homologacao = 'Fiscal';
                }

                foreach ($itens as $item) {
                    $link = site_url("homologacao/ficha/" . $item->id_item);
                    $partnumber = $item->part_number;
                    $owner_name = $this->owner_model->get_nome_owner_and_responsaveis($item->cod_owner);
                ?>
                    <tr class="click-select">
                        <td><?php
                            $textoDescricao = (strlen($item->descricao_atual) > 255) ? substr($item->descricao_atual, 0, 255) . "(...)" : $item->descricao_atual;
                            ?>
                            <input type="checkbox" <?php echo in_array($item->id_item, $bulk_selection) ? 'checked' : '' ?> class="chkitem item_selected" name="chkitem" value="<?php echo $item->id_item ?>" data-part-number="<?php echo $item->part_number ?>" data-estabelecimento="<?php echo $item->estabelecimento ?>" data-descricao="<?php echo $textoDescricao ?>" data-tipo="homologacao" />
                            <?php if ($item->sistema_origem == 'MANUAL') : ?>

                                <span class="label label-success" data-toggle="tooltip" title="MANUAL" style="margin-top:5px">M</span>
                            <?php endif; ?>
                        </td>
                        <td valign="top" align="left">
                            <div class="d-flex">

                                <?php if (in_array('pn_primario_secundario', $campos_adicionais)) : ?>
                                    <a style="margin-top: 1px;" href="<?php echo $link ?>">
                                        <div style=" white-space: break-spaces;"><?php echo $item->part_number ?> </div>
                                        <div style=" white-space: break-spaces;"><?php echo $item->pn_primario_mpn ?> </div>
                                        <div style=" white-space: break-spaces;"><?php echo $item->pn_secundario_ipn ?> </div>
                                    </a>
                                <?php else : ?>
                                    <a style="margin-top: 1px;" href="<?php echo $link ?>"><?php echo $partnumber ?></a>
                                <?php endif; ?>
                                <?php if (in_array('item_novo_ou_modificado', $campos_adicionais)) : ?>
                                    <?php echo !empty($item->integracao_novo_material) ? '(' . $item->integracao_novo_material . ')&nbsp;' : ''; ?>
                                <?php endif; ?>
                                <?php if ($item->item_ja_homologado == 1) : ?>
                                    <span data-toggle="tooltip" title="Item já homologado anteriormente" style="margin-left: 5px;">
                                        <img src="/assets/img/ok-icon.ico" alt="Ícone check verde para representar OK" width="16">
                                    </span>
                                <?php endif; ?>
                            </div>
                            <div class="">
                                <?php if ($simplus) : ?>
                                    <?php if ($item->status_simplus == null || $item->status_simplus == 0) : ?>
                                        <i class="fa fa-circle" style="color: gray" data-toggle="tooltip" title="Item pendente de envio para a Simplus"></i>
                                    <?php elseif ($item->status_simplus == 1) : ?>
                                        <i class="fa fa-circle" style="color: green" data-toggle="tooltip" title="Item enviado para Simplus"></i>
                                    <?php endif ?>
                                <?php endif ?>
                                <?php if ($has_status_exportacao) : ?>
                                    <?php if ($item->status_exportacao) : ?>
                                        <span style="color: white;background-color:#5cb85c;" class="badge" data-toggle="tooltip" title="Item exportado">
                                            <small>Exportado</small>
                                        </span>
                                    <?php else : ?>
                                        <span style="color: white;background-color:#af0000;" class="badge" data-toggle="tooltip" title="Item pendente">
                                            <small>Pendente</small>
                                        </span>
                                    <?php endif; ?>
                                <?php endif; ?>
                                <?php if (in_array('owner', $campos_adicionais)) : ?>
                                    <?php if ($item->id_status == 10) : ?>
                                        <span style="color: white;background-color:#af0000;" class="badge" data-toggle="tooltip" title="Item Pendente de Integração">
                                            <small>Pendente Integração</small>
                                        </span>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                            <?php if (!empty($item->wf_id)) : ?>
                                <span class="label" <?php echo "style=' background-color: $item->wf_color; color: #fff;' " ?> data-toggle="tooltip" title="MANUAL" style="margin-top:5px"><?php echo $item->wf_status_atributos ?></span>
                            <?php endif; ?>
                        </td>
                        <?php if ($multi_estabelecimentos == 1) { ?>
                            <td class="text-center"><?php echo $item->estabelecimento ? $item->estabelecimento : 'N/A'; ?></td>
                        <?php } ?>
                        <td style="min-width: 200px;">
                            <?php $textoDescricao = (strlen($item->descricao_atual) > 255) ? substr($item->descricao_atual, 0, 255) . "(...)" : $item->descricao_atual; ?>
                            <a href="<?php echo $link ?>" style="word-break: break-word;"><?php echo $textoDescricao ?></a>
                        </td>
                        <?php if (in_array('owner', $campos_adicionais)) : ?>
                            <td>

                                <?php echo $owner_name ?>
                            </td>
                        <?php else : ?>
                            <td>
                                <?php echo $item->descricao_mercado_local ?>
                            </td>
                        <?php endif; ?>
                        <?php if (in_array('owner', $campos_adicionais)) : ?>
                            <td><?php echo $item->empresa_prioridade ?></td>
                        <?php else : ?>
                            <td><?php echo $item->evento ?></td>
                        <?php endif; ?>
                        <td><?php echo $item->ncm_atual ?></td>
                        <td>
                            <?php echo $item->ncm_proposto ?>
                        </td>
                        <td class="text-center">
                            <?php

                            if (preg_replace("/[^0-9]/", "", $item->ncm_atual) != preg_replace("/[^0-9]/", "", $item->ncm_proposto)) {
                            ?>
                                <span class="glyphicon glyphicon-exclamation-sign" style="color: #af0000; font-size: 20px"></span>
                            <?php
                            }
                            ?>
                        </td>
                        <td class="text-center">
                            <?php
                            $dat_criacao = '';
                            $data_modificacao = '';
                            if (!empty($item->dat_criacao)) {
                                $dat_criacao = date("d/m/Y", strtotime($item->dat_criacao));
                                echo '<strong>DC:' . $dat_criacao . '</strong>';
                            }
                            if (!empty($item->data_modificacao)) {
                                $data_modificacao = date("d/m/Y", strtotime($item->data_modificacao));
                                echo '<br/><strong>DM:' . $data_modificacao . '</strong>';
                            }
                            ?>
                        </td>
                        <td class="text-center">
                            <?php

                            $homolog_info = $this->cad_item_model->get_homolog_info($item->id_item);

                            if (isset($homolog_info) && !empty($homolog_info)) {
                                $count_values = 0;
                                if (count($homolog_info) == 1) {

                                    if (
                                        ($homolog_info[0]->tipo_homologacao == 'Engenharia' && isset($homologacoes['homologacao_engenharia']) && $homologacoes['homologacao_engenharia'] == TRUE) ||
                                        ($homolog_info[0]->tipo_homologacao == 'Fiscal' && isset($homologacoes['homologacao_fiscal']) && $homologacoes['homologacao_fiscal'] == TRUE)
                                    ) {
                                        if ($homolog_info[0]->homologado == 1) {
                            ?>
                                            <h3 data-toggle="tooltip" title="<?php echo $homolog_info[0]->nome ?>" class="glyphicon glyphicon-thumbs-up text-green-next"></h3>
                                        <?php
                                        } else if ($homolog_info[0]->homologado == 0) {
                                        ?>
                                            <h3 data-toggle="tooltip" title="<?php echo $homolog_info[0]->nome ?>" class="glyphicon glyphicon-thumbs-down text-red-next"></h3>
                                        <?php
                                        } else {
                                        ?>
                                            <h3 data-toggle="tooltip" title="<?php echo $homolog_info[0]->nome ?>" class="glyphicon glyphicon-asterisk text-gray-next"></h3>
                                        <?php
                                        }
                                        $count_values++;
                                    }
                                    if (
                                        (isset($homologacoes['homologacao_engenharia']) && $homologacoes['homologacao_engenharia'] == TRUE ||
                                            isset($homologacoes['homologacao_fiscal']) && $homologacoes['homologacao_fiscal'] == TRUE) && count($homologacoes) == 2
                                    ) {
                                        $count_values++;
                                        ?>
                                        <h3 class="glyphicon glyphicon-thumbs-up text-muted-next"></h3>
                                    <?php
                                    }
                                    if ($count_values == 0) : ?>
                                        <?php if ($homologado) : ?>
                                            <h3 data-toggle="tooltip" title="<?php echo $homolog_info[0]->nome ?>" class="glyphicon glyphicon-thumbs-up text-green-next"></h3>
                                        <?php else : ?>
                                            <h3 class="glyphicon glyphicon-thumbs-up text-muted-next"></h3>
                                        <?php endif; ?>
                                        <?php
                                    endif;
                                } else {
                                    foreach ($homolog_info as $info) {
                                        if (
                                            ($info->tipo_homologacao == 'Engenharia' && isset($homologacoes['homologacao_engenharia']) && $homologacoes['homologacao_engenharia'] === TRUE) ||
                                            ($info->tipo_homologacao == 'Fiscal' && isset($homologacoes['homologacao_fiscal']) && $homologacoes['homologacao_fiscal'] === TRUE)
                                        ) {
                                            if ($info->homologado == 1) {
                                        ?>
                                                <h3 data-toggle="tooltip" title="<?php echo $info->nome ?>" class="glyphicon glyphicon-thumbs-up text-green-next"></h3>
                                            <?php
                                            } else if ($info->homologado == 0) {
                                            ?>
                                                <h3 data-toggle="tooltip" title="<?php echo $info->nome ?>" class="glyphicon glyphicon-thumbs-down text-red-next"></h3>
                                            <?php
                                            } else {
                                            ?>
                                                <h3 data-toggle="tooltip" title="<?php echo $info->nome ?>" class="glyphicon glyphicon-asterisk text-gray-next"></h3>
                                <?php
                                            }
                                        }
                                    }
                                }
                            } else {
                                ?>
                                <?php if (isset($homologacoes['homologacao_fiscal']) && $homologacoes['homologacao_fiscal'] == TRUE) : ?>
                                    <h3 class="glyphicon glyphicon-thumbs-up text-muted-next"></h3>
                                <?php endif; ?>
                                <?php if (isset($homologacoes['homologacao_engenharia']) && $homologacoes['homologacao_engenharia'] == TRUE) : ?>
                                    <h3 class="glyphicon glyphicon-thumbs-up text-muted-next"></h3>
                                <?php endif; ?>
                            <?php
                            }

                            ?>
                        </td>
                    </tr>
                <?php } ?>
            </tbody>
        </table>

        <div class="text-center">
            <?php echo $this->pagination->create_links() ?>
        </div>

        <script type="text/javascript" src="<?php echo base_url('assets/js/fancybox/jquery.mousewheel-3.0.6.pack.js') ?>"></script>

        <link rel="stylesheet" href="<?php echo base_url('assets/js/fancybox/jquery.fancybox.css?v=2.1.5') ?>" type="text/css" media="screen" />
        <script type="text/javascript" src="<?php echo base_url('assets/js/fancybox/jquery.fancybox.pack.js?v=2.1.5') ?>"></script>

        <link rel="stylesheet" href="<?php echo base_url('assets/js/fancybox/helpers/jquery.fancybox-buttons.css?v=1.0.5') ?>" type="text/css" media="screen" />
        <script type="text/javascript" src="<?php echo base_url('assets/js/fancybox/helpers/jquery.fancybox-buttons.js?v=1.0.5') ?>"></script>
        <script type="text/javascript" src="<?php echo base_url('assets/js/fancybox/helpers/jquery.fancybox-media.js?v=1.0.6') ?>"></script>

        <link rel="stylesheet" href="<?php echo base_url('assets/js/fancybox/helpers/jquery.fancybox-thumbs.css?v=1.0.7') ?>" type="text/css" media="screen" />
        <script type="text/javascript" src="<?php echo base_url('assets/js/fancybox/helpers/jquery.fancybox-thumbs.js?v=1.0.7') ?>"></script>

        <script type="text/javascript">
            $(function() {
                $('a.fancybox').fancybox({
                    'changeFade': 0,
                    'padding': 0,
                    'loop': false,
                    'type': 'image',
                    'helpers': {
                        overlay: {
                            locked: false
                        }
                    }
                });
            });
        </script>
    <?php } else { ?>
        <?php if (empty($filtered)): ?>
            <p class="lead">Utilize os filtros acima e clique em pesquisar para exibir os resultados</p>
        <?php else: ?>
            <p class="lead">Nenhum resultado encontrado</p>
        <?php endif; ?>

    <?php } ?>

<?php } ?>

<?php if ($simplus) : ?>
    <div class="modal fade" id="simplus-modal" tabindex="-1" role="dialog" aria-labelledby="Simplus" aria-hidden="true"></div>
<?php else : ?>
    <div class="modal fade" id="transferencia-modal" tabindex="-1" role="dialog" aria-labelledby="Transferência" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <?php echo form_open('homologacao/transferir_responsavel', array('class' => 'form-horizontal')) ?>
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    <h4 class="modal-title" id="myModalLabel">Transferência de responsável de <span class="bulk-selection-count">0</span> item(ns)</h4>
                </div>
                <div class="modal-body" style="margin: 15px;">
                    <input type="hidden" name="itens" value="" />
                    <input type="hidden" name="url_redirect" value="homologacao" />

                    <div class="form-group">
                        <div id="message_user_transferencia_responsavel"></div>
                        <label for="tipo_responsavel">Tipo de responsável:</label>
                        <select class="form-control selectpicker" name="tipo_responsavel" id="tipo_responsavel" data-show-subtext="true" data-title="Selecione o tipo de responsável">
                            <option value="">Selecione</option>
                            <option value="id_resp_engenharia">Engenheiro</option>
                            <option value="id_resp_fiscal">Fiscal</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Usuário responsável:</label>
                        <select class="form-control selectpicker" name="id_usuario" id="id_usuario" data-live-search="true" data-show-subtext="true" data-title="Selecione o novo responsável">
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Motivo:</label>
                        <textarea class="form-control" name="motivo_transf" id="motivo_transf" rows="3" placeholder="Motivo"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Fechar</button>
                    <button type="submit" class="btn btn-primary" id="button-transfer-submit" name="transferir-commit" value="1">Salvar</button>
                </div>
                <?php echo form_close(); ?>
            </div>
        </div>
    </div>
<?php endif ?>

<div class="modal fade" id="homologacao-modal" tabindex="-1" role="dialog" aria-labelledby="Homologação" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <?php echo form_open('', array('class' => 'form-horizontal')) ?>
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">Homologação de <span class="bulk-selection-count">0</span> item(ns)</h4>
            </div>

            <div class="modal-body" style="padding: 5px 20px">
                <input type="hidden" name="itens" value="" />

                <?php
                $texto_obsoleto = "Informado pelo(a) Sr(a). " . sess_user_nome() . " que o item está Inativo.";
                ?>
                <div class="row">
                    <div class="col-sm-2">
                        &nbsp;
                    </div>

                    <div class="col-sm-10" style="margin-bottom: 10px">
                        <div class="col-sm-12">
                            <div class="radio-inline text-success">
                                <label>
                                    <input type="radio" name="homologado" value="1" <?php echo set_radio('homologado', 1) ?> id="homologado1" value="option1" checked>
                                    <i class="glyphicon glyphicon-thumbs-up"></i> Homologado
                                </label>
                            </div>
                            <div class="radio-inline text-danger">
                                <label>
                                    <input type="radio" name="homologado" value="0" <?php echo set_radio('homologado', 0) ?> id="homologado0" value="option2">
                                    <i class="glyphicon glyphicon-thumbs-down"></i> Não Homologado
                                </label>
                            </div>
                            <div class="radio-inline">
                                <label>
                                    <input type="radio" name="homologado" value="2" <?php echo set_radio('homologado', 2) ?> id="homologado2" value="option3" data-text="<?php echo $texto_obsoleto ?>">
                                    <i class="glyphicon glyphicon-asterisk"></i> Inativo(s)
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row motivo-block">
                    <div class="col-sm-2">
                        <label for="motivo" class="pull-right control-label">Motivo: </label>
                    </div>

                    <div class="col-sm-10">
                        <div class="col-sm-12 motivo-select-block hide">
                            <select class="selectpicker form-control motivo-select" name="id_motivo_inativo">
                                <?php foreach ($motivos as $motivo) { ?>
                                    <option value="<?php echo $motivo->id_motivo ?>">
                                        <?php echo $motivo->motivo ?>
                                    </option>
                                <?php } ?>
                                <option value="">Outra situação</option>
                            </select>
                        </div>

                        <div class="col-sm-12 motivo-text-block" style="margin-top: 5px">
                            <textarea class="form-control" id="motivo" name="motivo" placeholder="Motivo" style="height: 100px"><?php echo set_value('motivo', isset($check_item->id_item) ? $check_item->motivo : "") ?></textarea>
                        </div>
                    </div>
                    <div class="col-sm-2"></div> <!-- Alinhamento dos checkboxes -->
                    <?php if ($can_homologar_atributos) : ?>
                        <div class="col-sm-10">
                            <div class="col-sm-12">
                                <div class="checkbox" style="margin-right: 2px; margin-top:5px;">
                                    <label for="ckbx_wf">
                                        <input id="ckbx_wf" name="ckbx_wf" type="checkbox" value="1" />
                                        <strong>Homologar também atributos</strong>
                                    </label>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="form-group" style="display: none">
                    <div class="col-sm-offset-2 col-sm-10">
                        <button type="submit" name="submit" value="1" class="btn btn-primary">Enviar</button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Fechar</button>
                <button type="submit" class="btn btn-primary" name="homologacao-commit" value="1">Salvar</button>
            </div>

            <?php echo form_close() ?>
        </div>
    </div>
</div>

<?php if ($empresa_pais) : ?>
    <?php $this->load->view('cadastros/mestre_itens/modal-multi-paises', 'empresa_pais', 'entry'); ?>
<?php endif; ?>

<!-- Modal de Filtros -->
<div id="exportOptionsModal" class="custom-modal" style="display: none;">
    <div class="custom-modal-content">
        <div class="custom-modal-header">
            <h5 class="custom-modal-title">Filtros</h5>
            <button type="button" class="custom-close-button" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="custom-modal-body">
            <p>Mostrar colunas na tabela</p>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" value="1" id="chkAtributos">
                <label class="form-check-label" for="chkAtributos">
                    Atributos
                </label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" value="1" id="chkPerguntasRespostas">
                <label class="form-check-label" for="chkPerguntasRespostas">
                    Perguntas e respostas
                </label>
            </div>
        </div>
        <div class="custom-modal-footer">
            <button type="button" class="btn btn-cancelar" id="btnCancelarModal">Cancelar</button>
            <button type="button" class="btn btn-exportar" id="btnExportarComFiltros" data-loading-text="Exportando...">Exportar</button>
        </div>
    </div>
</div>

<div id="loading-overlay">
    <div id="loading-message">Carregando...</div>
</div>
<script type="text/javascript">
    var items = [];

    function formDisable() {
        if (!$("#form_homologacao #commit").hasClass('disabled')) {
            $('#loading-overlay').show();
            $("#form_homologacao #commit").prepend('<span class="spinner-border" role="status" aria-hidden="true"></span> ').addClass('disabled')
        }
    }
    $(function() {
        count = '<?php echo count($bulk_selection) ?>';

        $("#homologacao-modal").on('show.bs.modal', function(e) {
            if (count == 0) {
                swal('Atenção', 'Selecione no mínimo um item para homologar.', 'warning')
                return false;
            }

            $(this).find('.bulk-selection-count').html(count);
        });

        $("#transferencia-modal").on('show.bs.modal', function(e) {
            if (count == 0) {
                swal('Atenção', 'Selecione no mínimo um item para transferir.', 'warning')
                return false;
            }

            $(this).find('.bulk-selection-count').html(count);
        });

        $("#form_homologacao #commit").on('click', function(e) {
            formDisable();
            $('#form_homologacao').submit();
        });
        $(".btn-modal-simplus").on('click', function(e) {
            if (count == 0) {
                swal('Atenção', 'Selecione no mínimo um item para enviar.', 'warning')
                return false;
            }

            $('#simplus-modal').modal('show');

            $.ajax({
                url: $(this).attr('href'),
                method: 'post',
                success: function(ret) {
                    $('#simplus-modal').html(ret);
                }
            });
        });

        //
        $(".list-group-opt input[name='list_opt'], select#atribuido_para").on('change', function() {
            // formDisable()
            // $(this).parents('form').submit();
        });

        $(".list-group-opt > label").tooltip({
            container: 'body',
            placement: 'bottom',
            html: 'true'
        });

        $('#homologacao-modal input[type="radio"]').on('change', function() {
            // Inativo
            if ($(this).val() == 2) {
                $('.motivo-block').find('.motivo-select-block').removeClass('hide');
                $('.motivo-block').find('.motivo-text-block').addClass('hide');
            } else {
                $('.motivo-block').find('.motivo-select-block').addClass('hide');
                $('.motivo-block').find('.motivo-text-block').removeClass('hide');
            }
        });

        $('.motivo-block').find('.motivo-select').on('change', function() {
            if ($(this).val() == '') {
                $('.motivo-block').find('.motivo-text-block').removeClass('hide');
            } else {
                $('.motivo-block').find('.motivo-text-block').addClass('hide');
            }
        });

        $("#status_implementacao").on('change', function() {
            if (!$(this).val()) {
                $(this).selectpicker('selectAll');
            }
        });

        $("#status_exportacao").on('change', function() {
            if (!$(this).val()) {
                $(this).selectpicker('selectAll');
            }
        });

        $("#select-grupo-tarifario").on('change', function() {
            $('input[name="id_grupo_tarifario"]').val($(this).val());
            $.ajax({
                url: '<?php echo site_url("homologacao/bulk_clean") ?>',
                success: function() {
                    formDisable();
                    $('#form_homologacao').submit();
                }
            });
        });

        <?php if ($simplus) : ?>
            $(".form-simplus").on('submit', function() {
                $(this).find('button[type="submit"]').button('loading');
            });
        <?php endif ?>

        // $(".btn-download-xls").on('click', function() {
        //     var button = $(this);
        //     $('#loading-overlay').show();
        //     $(button).button('loading');

        //     $.ajax({
        //         type: 'get', 
        //         url: '?exportar=1', 
        //         xhrFields: {
        //             responseType: 'blob'
        //         },
        //         success: function(response) {
        //             if (response instanceof Blob) {
        //                 var blobUrl = window.URL.createObjectURL(response);

        //                 var linkDownload = document.createElement('a');
        //                 linkDownload.href = blobUrl;
        //                 linkDownload.download = 'exportar.xlsx'; 

        //                 document.body.appendChild(linkDownload);
        //                 linkDownload.click();
        //                 document.body.removeChild(linkDownload);

        //                 window.URL.revokeObjectURL(blobUrl);
        //             } else {
        //                 $('#loading-overlay').hide();
        //                 console.error('Resposta inválida:', response);
        //             }
        //         },
        //         error: function(xhr, status, error) {
        //              $('#loading-overlay').hide();
        //         },
        //         complete: function(response) {
        //              $(button).button('reset');
        //             $('#loading-overlay').hide();
        //         }
        //     });
        // });

        $(".btn-download-xls").on('click', function() {
            // Limpa os checkboxes ao abrir o modal
            $('#chkAtributos').prop('checked', false);
            $('#chkPerguntasRespostas').prop('checked', false);
            // Mostra o modal
            $('#exportOptionsModal').fadeIn(200); // Usando fadeIn para uma transição suave
        });

        // Manipulador para fechar o modal (botão "X" e "Cancelar")
        $('#btnCancelarModal, #exportOptionsModal .custom-close-button').on('click', function() {
            $('#exportOptionsModal').fadeOut(200); // Usando fadeOut
        });

        // Manipulador para fechar o modal ao clicar fora dele
        $('#exportOptionsModal').on('click', function(event) {
            if ($(event.target).is('#exportOptionsModal')) {
                $(this).fadeOut(200);
            }
        });

        // Manipulador para o botão "Exportar" dentro do modal
        $("#btnExportarComFiltros").on('click', function() {
            var exportButton = $(this); // Botão "Exportar" do modal
            var originalButtonText = exportButton.html(); // Salva o texto original do botão

            $('#loading-overlay').show(); // Mostra o overlay de carregamento global

            // Se você estiver usando o plugin de botão do Bootstrap:
            if (typeof exportButton.button === 'function') {
                exportButton.button('loading'); // Coloca o botão em estado de carregamento
            } else {
                // Fallback se o plugin Bootstrap não estiver disponível
                exportButton.prop('disabled', true).html('Exportando...');
            }

            // Monta os parâmetros da query string
            var params = {
                exportar: 1,
                atributos: $('#chkAtributos').is(':checked') ? 1 : 0,
                perguntas_respostas: $('#chkPerguntasRespostas').is(':checked') ? 1 : 0
            };

            $.ajax({
                type: 'get',
                url: '?' + $.param(params), // Adiciona os parâmetros à URL base (ajuste '?' se sua URL base já tiver outros)
                xhrFields: {
                    responseType: 'blob'
                },
                success: function(response) {
                    $('#exportOptionsModal').fadeOut(200); // Esconde o modal em caso de sucesso
                    if (response instanceof Blob) {
                        var blobUrl = window.URL.createObjectURL(response);

                        var linkDownload = document.createElement('a');
                        linkDownload.href = blobUrl;
                        linkDownload.download = 'exportar.xlsx'; // Nome do arquivo

                        document.body.appendChild(linkDownload);
                        linkDownload.click();
                        document.body.removeChild(linkDownload);

                        window.URL.revokeObjectURL(blobUrl);
                    } else {
                        $('#loading-overlay').hide(); // Esconde o overlay se a resposta não for Blob
                        console.error('Resposta inválida do servidor:', response);
                        // Adicione um feedback visual para o usuário aqui, se necessário
                        alert('Erro: A resposta do servidor não foi um arquivo válido.');
                    }
                },
                error: function(xhr, status, error) {
                    $('#loading-overlay').hide(); // Esconde o overlay em caso de erro
                    console.error('Erro na exportação:', status, error, xhr.responseText);
                    // Adicione um feedback visual para o usuário aqui
                    alert('Ocorreu um erro ao exportar os dados: ' + error);
                },
                complete: function() {
                    $('#loading-overlay').hide(); // Garante que o overlay seja escondido

                    // Restaura o botão "Exportar" do modal
                    if (typeof exportButton.button === 'function') {
                        exportButton.button('reset');
                    } else {
                        exportButton.prop('disabled', false).html(originalButtonText);
                    }
                }
            });
        });

        $(".btn-gerar-xls").on('click', function() {
            let button = $(this);
            $('#loading-overlay').show();
            $(button).button('loading');

            $.ajax({
                type: 'get',
                url: '?gerar_exportacao_itens_to_api=1',
                dataType: 'json',
                success: function(response, status, xhr) {
                    if (response.success) {
                        // Redireciona para a URL do arquivo para iniciar o download
                        window.location.href = response.file_path;
                    } else {
                        console.error('Erro ao gerar a planilha:', response.error);
                        swal('Erro', 'Erro ao gerar a planilha: ' + response.error, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erro ao gerar a planilha:', error);
                    swal('Erro', 'Erro ao gerar a planilha: ' + error, 'error');
                },
                complete: function() {
                    $('#loading-overlay').hide();
                    $(button).button('reset');
                }
            });

        });

        function pollStatus(processId) {
            var statusUrl = `https://planilha-atributos.becomex.com.br/id/${processId}/status`;
            var pollInterval = setInterval(function() {
                $.ajax({
                    url: statusUrl,
                    type: 'GET',
                    xhrFields: {
                        withCredentials: true
                    },
                    success: function(data) {
                        updateStatusDisplay(data);
                        if (data.status === 'ready') {
                            clearInterval(pollInterval);
                            initiateDownload(processId);
                        } else if (data.status === 'error') {
                            clearInterval(pollInterval);
                            console.error('Erro no processamento');
                            $('#loading-overlay').hide();
                        }
                    },
                    error: function() {
                        console.error('Erro ao verificar status');
                        clearInterval(pollInterval);
                        $('#loading-overlay').hide();
                    }
                });
            }, 1000); // Polling a cada 1 segundo
        }

        function updateStatusDisplay(data) {
            var statusHTML = "";
            if (data.status === 'processing') {
                statusHTML += "<h3>Processando...</h3>";
            } else if (data.status === 'ready') {
                statusHTML += "<h3>Concluído!</h3>";
            } else if (data.status === 'error') {
                statusHTML += "<h3>Erro</h3>";
            }

            data.log_messages.forEach(function(message) {
                statusHTML += "<p>" + message + "</p>";
            });

            $('#loading-overlay').html(statusHTML);
        }

        function initiateDownload(processId) {
            var downloadUrl = `https://planilha-atributos.becomex.com.br/id/${processId}/download`;
            window.location.href = downloadUrl;
            $('#loading-overlay').hide();
            $('.btn-gerar-xls').button('reset');
        }

        $(".btn-paises-xls").on('click', function() {
            var button = $(this);
            $('#loading-overlay').show();
            $(button).button('loading');
            $.ajax({
                type: 'get',
                url: '?exportar_paises=1',
                xhrFields: {
                    responseType: 'blob'
                },
                success: function(response) {
                    if (response instanceof Blob) {
                        var blobUrl = window.URL.createObjectURL(response);

                        var linkDownload = document.createElement('a');
                        linkDownload.href = blobUrl;
                        linkDownload.download = 'paises_homologacao.xlsx';
                        document.body.appendChild(linkDownload);
                        linkDownload.click();

                        document.body.removeChild(linkDownload);
                        window.URL.revokeObjectURL(blobUrl);
                    } else {
                        console.error('Resposta inválida:', response);
                    }
                },
                error: function(xhr, status, error) {
                    $('#loading-overlay').hide();
                },
                complete: function() {
                    $(button).button('reset');
                    $('#loading-overlay').hide();
                }
            }).then(function() {
                $('#loading-overlay').hide();
            });

        });

        const base_url = "<?php echo base_url() ?>";

        $('#modal-multi-paises-diana').on('click', function() {
            $("#message_user_transfer_owner_paises").hide();


            let checked_itens_to_owners_diana = $('input[name="chkitem"]:checked');

            if (checked_itens_to_owners_diana.length == 0) {
                swal('Atenção', 'Selecione um item para configurar os países', 'warning');
                return false;
            }

            if (checked_itens_to_owners_diana.length > 1) {
                swal('Atenção', 'Selecione somente um item para configurar os países', 'warning');
                return false;
            }

            $("#input-part-number-modal").val(checked_itens_to_owners_diana.data("part-number"));
            $("#estabelecimento-modal").val(checked_itens_to_owners_diana.data("estabelecimento"));
            $("#descricao-modal").val(checked_itens_to_owners_diana.data("descricao"));
            $("#reload_page").val("N");
            var id_empresa = "<?php echo $entry->id_empresa ?>";

            $.ajax({
                url: base_url + 'cadastros/mestre_itens/ajax_get_dados_modal_paises',
                method: 'POST',
                data: {
                    part_number: checked_itens_to_owners_diana.data("part-number"),
                    estabelecimento: checked_itens_to_owners_diana.data("estabelecimento"),
                    id_empresa: id_empresa
                },
                dataType: 'json',
                success: function(dados) {

                    if (dados.entry.ncm_proposto != null) {
                        $('#ncmbrasil-modal').val(dados.entry.ncm_proposto);
                        $('#btn-salvar-paises').prop('disabled', false);
                    }

                    if (dados && dados.empresa_pais) {
                        dados.empresa_pais.forEach(pais => {
                            const slug = pais.slug;
                            if (pais.item_pais.length > 0) {
                                pais.item_pais.forEach(item => {
                                    // Atribuindo os valores aos campos usando jQuery
                                    $('#codigo_classificacao_' + slug).val(item.codigo_classificacao);
                                    $('#desc_curta_' + slug).val(item.descricao_curta);
                                    $('#desc_completa_' + slug).val(item.descricao_completa);
                                    $('#desc_li_' + slug).val(item.li);
                                    $('#desc_adicional_' + slug).val(item.informacao_adicional);
                                });
                            } else {
                                $('#codigo_classificacao_' + slug).val("");
                                $('#desc_curta_' + slug).val("");
                                $('#desc_completa_' + slug).val("");
                                $('#desc_li_' + slug).val("");
                                $('#desc_adicional_' + slug).val("");
                            }
                            if (dados.entry.ncm_proposto != null) {
                                $('#codigo_classificacao_' + slug).removeAttr('disabled');
                                $('#desc_curta_' + slug).removeAttr('disabled');
                                $('#desc_completa_' + slug).removeAttr('disabled');
                                $('#desc_li_' + slug).removeAttr('disabled');
                                $('#desc_adicional_' + slug).removeAttr('disabled');
                            }
                        });
                    }
                },
                error: function() {
                    $('#message_user_transfer_owner_paises').html('<div class="alert alert-danger"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>Erro ao enviar os dados para o servidor.</div>');
                    $('#message_user_transfer_owner_paises').show();
                    $('#loading-img').hide();
                    $('#btn-salvar-paises').prop('disabled', false);
                    $('#btn-salvar-paises').html('Salvar');
                }
            });

        });

        function exportar_planilha_upload(badge, button) {
            $('#loading-overlay').show();
            $(button).button('loading');

            var form = $("#search_form_id");
            $.ajax({
                type: 'post',
                url: "<?php echo site_url('homologacao?exportar_planilha_upload=1') ?>",
                data: form.serialize(),
                xhrFields: {
                    responseType: 'blob'
                },
                success: function(response, status, xhr) {
                    if (response instanceof Blob) {
                        var blobUrl = window.URL.createObjectURL(response);
                        var linkDownload = document.createElement('a');
                        linkDownload.href = blobUrl;
                        linkDownload.download = 'planilha_upload.xlsx';
                        document.body.appendChild(linkDownload);
                        linkDownload.click();

                        document.body.removeChild(linkDownload);
                        window.URL.revokeObjectURL(blobUrl);
                    } else {
                        console.error('Resposta inválida:', response);
                    }
                },
                error: function(xhr, status, error) {
                    $('#loading-overlay').hide();
                },
                complete: function() {
                    $(button).button('reset');
                    $('#loading-overlay').hide();
                }
            });
        }

        $(".btn-download-xls-upload").on('click', function() {
            var button = $(this);
            $(button).button('loading');
            $('#loading-overlay').show();
            var badge = $('#table-list tbody tr td span.badge');
            var existem_itens_exportados = false;
            $.ajax({
                url: '<?php echo site_url("homologacao/verificar_itens_exportados") ?>',
                success: function(e) {
                    if (e.status == 200 && e.data.exportados) {
                        existem_itens_exportados = true;
                    }

                    if (existem_itens_exportados) {
                        $('#loading-overlay').hide();
                        swal({
                            title: "Atenção!",
                            text: "Alguns itens com status <strong>exportado</strong> serão novamente exportados. Deseja continuar?",
                            type: "warning",
                            confirmButtonText: "OK",
                            cancelButtonText: "Cancelar",
                            showConfirmButton: true,
                            showCancelButton: true,
                            allowOutsideClick: false
                        }).then(function() {
                            exportar_planilha_upload(badge, button);
                        }, function() {
                            $(button).button('reset');
                        });
                    } else {
                        exportar_planilha_upload(badge, button);
                    }
                }
            })
        })

        $("#homologacao-modal form").on('submit', function(e) {
            e.preventDefault();

            // Adicionar classe de loading ao botão
            let button = $(this).find('button[type="submit"]');
            $(button).button('loading');

            var formData = $(this).serialize();

            $.ajax({
                url: '<?php echo site_url("homologacao/ajax_homologar") ?>',
                method: 'POST',
                data: formData,
                success: function(response) {
                    console.log(response, 'success');
                    if (response.status === 'warning' && response.atributos_vazios) {
                        if (response.pode_homologar) {
                            // Usuário tem permissão para homologar mesmo com atributos vazios
                            swal({
                                title: 'Atenção!',
                                text: 'Existem atributos obrigatórios que ainda não foram preenchidos. Deseja continuar mesmo assim?',
                                type: 'warning',
                                confirmButtonText: "Sim",
                                cancelButtonText: "Não",
                                showConfirmButton: true,
                                showCancelButton: true
                            }).then(function(result) {
                                if (result) {
                                    // Usuário escolheu continuar
                                    $.ajax({
                                        url: '<?php echo site_url("homologacao/ajax_homologar") ?>',
                                        method: 'POST',
                                        data: formData + '&forcar_homologacao=1',
                                        success: function(resp) {
                                            if (resp.status === 'success') {
                                                swal({
                                                    title: 'Sucesso!',
                                                    text: resp.message,
                                                    type: 'success'
                                                }).then(function() {
                                                    window.location.href = resp.redirect_url;
                                                });
                                            }
                                        }
                                    });
                                } else {
                                    window.location.href = response.redirect_url;
                                }
                            }).catch(function() {
                                window.location.href = response.redirect_url;
                            })
                        } else {
                            // Usuário não tem permissão para homologar com atributos vazios
                            swal({
                                title: 'Atenção!',
                                text: 'Existem atributos obrigatórios que ainda não foram preenchidos. Por causa disso, os atributos não foram homologados. Verifique os itens.',
                                type: 'warning'
                            }).then(function() {
                                window.location.href = response.redirect_url;
                            });
                        }
                    } else if (response.status === 'success') {
                        swal({
                            title: 'Sucesso!',
                            text: response.message,
                            type: 'success'
                        }).then(function() {
                            window.location.href = response.redirect_url;
                        });
                    } else {
                        console.log(response, 'error');
                        swal({
                            title: 'Erro!',
                            text: response.message,
                            type: 'error'
                        }).then(function() {
                            $(button).button('reset');
                            window.location.href = response.redirect_url;
                        })
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erro na requisição:', status, error);
                    $(button).button('reset');
                    window.location.reload();
                }
            });
        });
    });

    /* Bulk Selection */
    $('#chk-select-all').on('change', function(e) {
        var action = e.target.checked ? 'add' : 'rm';

        if (e.target.checked) {
            var action = 'add';
            var items = $("#table-list").find('input[name="chkitem"]:not(:checked)').not(':disabled').map(function() {
                return $(this).val();
            }).get();
        } else {
            var action = 'rm';
            var items = $("#table-list").find('input[name="chkitem"]:checked').not(':disabled').map(function() {
                return $(this).val();
            }).get();
        }

        if (items.length > 0) {
            bulk_selection_save(items, action);
        }

        $("#table-list").find('input[name="chkitem"]').not(':disabled').prop('checked', e.target.checked);
    });

    $("#ckbx_busca_descricao, #ckbx_busca_part_number").on('click', function(ev) {
        var value = +$(this).is(':checked');
        var rel = $(this).attr('rel');

        $('input[name="' + rel + '"]').val(value);
    });

    function bulk_selection_save(items, action) {
        $('.bulk-info-actions').hide();
        $('.bulk-selection-box').show();
        $('.bulk-selection-loading').html('<strong>Aguarde!</strong> Salvando seleções...').show();

        $.ajax({
            url: '<?php echo site_url("homologacao/ajax_bulk_save") ?>',
            method: 'post',
            data: {
                items: items,
                action: action
            },
            success: function(response) {
                var data = $.parseJSON(response);
                bulk_selection_box_update(data.total_items, data.select_all);
            }
        });
    }

    function bulk_selection_box_update(total_items, select_all) {
        $('.bulk-selection-loading').hide();
        $('.bulk-info-actions').show();

        if (select_all == true) {
            $('.bulk_select_all').show();
        } else {
            $('.bulk_select_all').hide();
        }

        if (total_items == 0) {
            $('.bulk-selection-box').hide();
        } else {
            $('.bulk-selection-box').show();
        }

        $('.bulk-selection-count').html(total_items);

        count = total_items;
    }

    function bulk_select_all() {
        if ($('.bulk_select_all').hasClass('disabled')) {
            return false;
        }

        $('.bulk-info-actions').hide();
        $('.bulk-selection-loading').html('<strong>Aguarde!</strong> Selecionando todos os itens...').show();

        var post_data = $("#form_homologacao").serialize();

        $.ajax({
            url: '<?php echo site_url("homologacao/ajax_bulk_select_all") ?>',
            method: 'post',
            data: post_data,
            success: function(response) {
                var data = $.parseJSON(response);
                $('#chk-select-all, .chkitem').prop('checked', 'checked');
                bulk_selection_box_update(data.total_items, false);
            }
        }).done(function() {
            $('.bulk-selection-loading').hide();
            $('.bulk-info-actions').show();
        });
    }

    function bulk_clean() {
        $('.chkitem').prop('checked', false);

        $.ajax({
            url: '<?php echo site_url("homologacao/bulk_clean") ?>'
        });

        bulk_selection_box_update(0, 'update');
    }

    $(function() {
        if ($('.bulk_select_all').hasClass('disabled'))
            $('.bulk_select_all').tooltip();

        $('.chkitem').click(function() {
            var check_val = $(this).val();
            var items = [check_val];

            if ($(this).is(':checked')) {
                bulk_selection_save(items, 'add');
            } else {
                bulk_selection_save(items, 'rm');
            }
        });

        function setAlert(field, event) {
            event.preventDefault();

            $("#message_user_transferencia_responsavel").addClass("alert alert-danger");
            $("#message_user_transferencia_responsavel").html(`<div>Oops! ${field} para concluir a transferência.</div>`);
        }

        $("#button-transfer-submit").on("click", function(e) {
            if (!$("#motivo_transf").val()) {
                setAlert("Digite um motivo", e);
            }

            if (!$("#id_usuario").val()) {
                setAlert("Selecione o novo usuário responsável", e);
            }

            if (!$("#tipo_responsavel").val()) {
                setAlert("Selecione o tipo do novo usuário responsável", e);
            }

            if ($("#tipo_responsavel").val() && $("#id_usuario").val()) {
                $("#message_user_transfer").find("div").remove();
            }
        });

        $("#tipo_responsavel").on("change", function() {
            if ($(this).val()) {
                $.post("<?php echo base_url() ?>homologacao/xhr_get_responsaveis", {
                    "tipo_responsavel": $(this).val()
                }).done(function(data) {
                    // console.log(data);
                    populateSelect(data.select_body);
                });
            } else {
                populateSelect("");
            }
        });

        $("#transferencia-modal").on("hide.bs.modal", () => resetFormData());

        function resetFormData() {
            populateSelect("");

            $("#tipo_responsavel").selectpicker("val", "");
            $("#tipo_responsavel").selectpicker("refresh");
        }

        function populateSelect(content) {
            $("#id_usuario").html(content);
            $("#id_usuario").selectpicker("refresh");
        }
    });

    jQuery(document).ready(function() {
        $('.click-select').on('click', function(e) {
            if (e.target.nodeName != 'INPUT') {
                $(this).find('input').click();
            }
        })
    });

    $("#btn-lista_impacto-xls").on('click', function() {
        $('#loading-overlay').show();
        $(this).prop('disabled', true);

        $.ajax({
            type: 'get',
            url: "<?php echo site_url('homologacao/lista_impacto') ?>",
            xhrFields: {
                responseType: 'blob'
            },
            success: function(response, status, xhr) {
                if (response instanceof Blob) {
                    var blobUrl = window.URL.createObjectURL(response);
                    var linkDownload = document.createElement('a');
                    linkDownload.href = blobUrl;
                    linkDownload.download = 'lista_impacto.xlsx';

                    document.body.appendChild(linkDownload);

                    linkDownload.click();
                    document.body.removeChild(linkDownload);
                    window.URL.revokeObjectURL(blobUrl);
                } else {
                    console.error('Resposta inválida:', response);
                }
            },
            error: function(xhr, status, error) {
                console.error('Erro na requisição:', status, error);
            },
            complete: function() {
                $('#loading-overlay').hide();
                $(this).prop('disabled', false);
            }
        });
    });
</script>

<script>
    var checked_items = [];

    function exportItems() {
        $('#table-list').find('input[name="chkitem"]:checked').not(':disabled').map(function() {
            var inputElement = $(this);
            var part_number = inputElement.attr('data-part-number');
            checked_items.push(part_number);
        }).get();

        // Cria a query string com os atributos part_number usando o mesmo nome de parâmetro
        var queryString = checked_items.map(function(item) {
            return 'part_number[]=' + encodeURIComponent(item);
        }).join('&');

        return queryString;
    }

    function populateItens() {
        $.ajax({
            url: "<?php echo site_url('homologacao/xhr_get_itens') ?>",
            method: 'post',
            success: function(ret) {
                $('#simplus-modal').html(ret);
            }
        });
    }

    $(document).ready(function() {
        $('#desvincularGrupo').on('click', function(e) {
            e.preventDefault();
            var queryString = exportItems();
            console.log(queryString);

            // Concatena a query string na URL da rota
            var baseUrl = "<?php echo site_url('atribuir_grupo/deletar'); ?>";
            var exportUrl = baseUrl + '?' + queryString;

            // Redireciona para a rota com a query string
            window.location.href = exportUrl;
        });

        /* 
            IMplementação de carregamento de filtros via assincrona quando o usuário escolhe um filtro
            A função e chamada quando o usuário escolhe um filtro
        */
        const base_url = '<?php echo base_url(); ?>';
        let eventosSelecionados = <?php echo json_encode($this->cad_item_model->get_state('filter.evento')) ?> || [];
        let sistemasOrigemSelecionados = <?php echo json_encode($this->cad_item_model->get_state('filter.sistema_origem')) ?> || [];
        let exIpiSelecionados = <?php echo json_encode($this->cad_item_model->get_state('filter.ex_ipi_modal')) ?> || [];
        let exIiSelecionados = <?php echo json_encode($this->cad_item_model->get_state('filter.ex_ii_modal')) ?> || [];
        let prioridadesSelecionadas = <?php echo json_encode($this->cad_item_model->get_state('filter.prioridade')) ?> || [];
        let ncmsSelecionados = <?php echo json_encode($this->cad_item_model->get_state('filter.ncm_proposta_modal')) ?> || [];
        let ownersSelecionados = <?php echo json_encode($this->cad_item_model->get_state('filter.owner')) ?> || [];

        let eventosLoaded = false;
        let sistemasOrigemLoaded = false;
        let exIpiLoaded = false;
        let exIiLoaded = false;
        let prioridadesLoaded = false;
        let ncmsLoaded = false;
        let ownersLoaded = false;
        let ncmPropostoLoaded = false;

        function inicializarEventosSelecionados() {
            if (eventosSelecionados && eventosSelecionados.length > 0) {
                if (!eventosLoaded) {
                    carregarEventos();
                } else {
                    $('#evento').selectpicker('val', eventosSelecionados);
                    $('#evento').selectpicker('refresh');
                }
            }
        }

        /**
         * Verifica se h  Prioridades selecionadas e se sim, carrega as Prioridades se n o estiverem carregadas
         * e marca as Prioridades que est o na lista de Selecionados.
         */
        function inicializarPrioridadesSelecionadas() {
            if (prioridadesSelecionadas && prioridadesSelecionadas.length > 0) {
                if (!prioridadesLoaded) {
                    carregarPrioridades();
                } else {
                    $('#prioridade').selectpicker('val', prioridadesSelecionadas);
                    $('#prioridade').selectpicker('refresh');
                }
            }
        }

        function inicializarOwnersSelecionados() {
            if (ownersSelecionados && ownersSelecionados.length > 0) {
                if (!ownersLoaded) {
                    carregarOwners();
                } else {
                    $('#owner').selectpicker('val', ownersSelecionados);
                    $('#owner').selectpicker('refresh');
                }
            }
        }

        function inicializarSistemasOrigemSelecionados() {
            if (sistemasOrigemSelecionados && sistemasOrigemSelecionados.length > 0) {
                if (!sistemasOrigemLoaded) {
                    carregarSistemasOrigem();
                    $('#collapseOne').collapse('show');
                } else {
                    $('#sistema_origem').selectpicker('val', sistemasOrigemSelecionados);
                    $('#sistema_origem').selectpicker('refresh');
                }
            }
        }

        function inicializarNcmSelecionados() {
            if (ncmsSelecionados && ncmsSelecionados.length > 0) {
                if (!ncmsLoaded) {
                    carregarNcms();
                    $('#collapseOne').collapse('show');
                } else {
                    $('#ncm_proposta_modal').selectpicker('val', ncmSelecionados);
                    $('#ncm_proposta_modal').selectpicker('refresh');
                }
            }
        }

        function carregarEventos() {
            if (eventosLoaded) return;

            $('#evento').empty().append('<option value="">Carregando...</option>');
            $('#evento').selectpicker('refresh');

            $.ajax({
                url: base_url + 'homologacao/get_list_eventos',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    $('#evento').empty().append('<option value="">Todos </option>');
                    $('#evento').empty().append('<option value="sem_evento"><strong>Sem Evento/Pacote</strong></option>');


                    if (response && response.length > 0) {
                        $.each(response, function(_, item) {
                            $('#evento').append(`<option value="${item.evento}">${item.evento}</option>`);
                        });
                    }

                    if (eventosSelecionados && eventosSelecionados.length > 0) {
                        $('#evento').selectpicker('val', eventosSelecionados);
                    }

                    $('#evento').selectpicker('refresh');
                    eventosLoaded = true;
                },
                error: function(err) {
                    console.error('Erro ao carregar Eventos:', err);
                    $('#evento').empty().append('<option value="">Erro ao carregar</option>');
                    $('#evento').selectpicker('refresh');
                }
            });
        }

        function carregarPrioridades() {
            if (prioridadesLoaded) return;

            $("#prioridade").empty().append('<option value="-1">Carregando...</option>');
            $("#prioridade").selectpicker("refresh");

            $.ajax({
                url: base_url + "homologacao/get_list_prioridades_by_empresa",
                type: "GET",
                dataType: "json",
                success: function(response) {
                    if (response && !response.error) {
                        let options = '';

                        response.forEach(function(prioridade) {
                            const value = prioridade.id_prioridade;
                            const text = `${prioridade.nome}`;
                            const isSelected =
                                prioridadesSelecionadas && prioridadesSelecionadas.includes(value);

                            options += `<option value="${value}" ${
                        isSelected ? "selected" : ""
                    }>${text}</option>`;
                        });

                        $("#prioridade").html(options);
                        $("#prioridade").selectpicker("refresh");
                        prioridadesLoaded = true;
                    } else {
                        console.error(
                            "Erro ao carregar Prioridades:",
                            response ? response.message : "Erro desconhecido"
                        );
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Erro na requisição:", error);
                    console.log("Resposta do servidor:", xhr.responseText);
                },
            });
        }

        function carregarOwners() {
            if (ownersLoaded) return;

            $("#owner").empty().append('<option value="-1">Carregando...</option>');
            $("#owner").selectpicker("refresh");

            $.ajax({
                url: base_url + "homologacao/get_list_owners_by_empresa",
                type: "GET",
                dataType: "json",
                success: function(response) {
                    if (response && !response.error) {
                        let options = '';

                        response.forEach(function(owner) {
                            const value = owner.codigo;
                            const text = `${owner.codigo} - ${owner.descricao}` + " - " + `${owner.nomes}`;
                            const isSelected =
                                ownersSelecionados && ownersSelecionados.includes(value);

                            options += `<option value="${value}" ${
                        isSelected ? "selected" : ""
                    }>${text}</option>`;
                        });

                        $("#owner").html(options);
                        $("#owner").selectpicker("refresh");
                        ownersLoaded = true;
                    } else {
                        console.error(
                            "Erro ao carregar Owners:",
                            response ? response.message : "Erro desconhecido"
                        );
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Erro na requisição:", error);
                    console.log("Resposta do servidor:", xhr.responseText);
                },
            });
        }

        function carregarSistemasOrigem() {
            if (sistemasOrigemLoaded) return;

            $('#sistema_origem').html('<option value="">Carregando...</option>');
            $('#sistema_origem').selectpicker('refresh');

            $.ajax({
                url: base_url + 'homologacao/get_list_sistemas_origens',
                type: 'GET',
                dataType: 'json',
                success: function(response) {

                    if (response && response.length > 0) {
                        let options = '';
                        $.each(response, function(_, sistemaOrigem) {
                            options += `<option value="${sistemaOrigem}">${sistemaOrigem}</option>`;
                        });
                        $('#sistema_origem').html(options);
                        $('#sistema_origem').selectpicker('refresh');
                        sistemasOrigemLoaded = true;
                    }

                    if (sistemasOrigemSelecionados && sistemasOrigemSelecionados.length > 0) {
                        $('#sistema_origem').selectpicker('val', sistemasOrigemSelecionados);
                    }

                    $('#sistema_origem').selectpicker('refresh');
                    sistemasOrigemLoaded = true;
                },
                error: function(err) {
                    console.error('Erro ao carregar Sistemas de Origem:', err);
                    $('#sistema_origem').empty().append('<option value="">Erro ao carregar</option>');
                    $('#sistema_origem').selectpicker('refresh');
                }
            });
        }

        /**
         * Carrega a lista de NCMS via AJAX e atualiza o selectpicker de NCM Proposto.
         * Se j  tiver carregado, n o faz nada.
         * Se n o tiver carregado ainda, carrega a lista de NCMS e marca os NCMS selecionados.
         */
        function carregarNcms() {
            if (ncmsLoaded) return;

            $("#ncm_proposta_modal").empty().append('<option value="-1">Carregando...</option>');
            $("#ncm_proposta_modal").selectpicker("refresh");

            $.ajax({
                url: base_url + "cadastros/mestre_itens/get_list_ncms_by_empresa",
                type: "GET",
                dataType: "json",
                success: function(response) {
                    if (response && !response.error) {
                        console.log(response);
                        let options = '<option value="-1">Todos os ncms propostos</option>';

                        response.forEach(function(ncm) {
                            const value = ncm;
                            const isSelected = ncmsSelecionados && ncmsSelecionados.includes(value);

                            options += `<option value="${value}" ${
                            isSelected ? "selected" : ""
                        }>${value}</option>`;
                        });

                        $("#ncm_proposta_modal").html(options);
                        $("#ncm_proposta_modal").selectpicker("refresh");
                        ncmsLoaded = true;
                    } else {
                        console.error(
                            "Erro ao carregar NCMS:",
                            response ? response.message : "Erro desconhecido"
                        );
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Erro na requisição:", error);
                    console.log("Resposta do servidor:", xhr.responseText);
                },
            });
        }

        inicializarEventosSelecionados();
        inicializarPrioridadesSelecionadas();
        inicializarOwnersSelecionados();
        inicializarSistemasOrigemSelecionados();
        inicializarNcmSelecionados();

        $('#evento').on('show.bs.select', function() {
            carregarEventos();
        });

        $('#prioridade').on('show.bs.select', function() {
            carregarPrioridades();
        });

        $('#owner').on('show.bs.select', function() {
            carregarOwners();
        });

        $('#sistema_origem').on('show.bs.select', function() {
            carregarSistemasOrigem();
        });

        $('#ncm_proposta_modal').on('show.bs.select', function() {
            carregarNcms();
        });
    })
</script>
<style>
    #loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        display: none;
    }

    #loading-message {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 24px;
    }

    /* Estilos para o Modal de Exportação */
    .custom-modal {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1050;
        /* Certifique-se que está acima de outros elementos */
        width: 100%;
        height: 100%;
        overflow: hidden;
        outline: 0;
        background-color: rgba(0, 0, 0, 0.5);
        /* Fundo escurecido */
        display: flex;
        /* Para centralizar o modal-content */
        align-items: center;
        /* Centraliza verticalmente */
        justify-content: center;
        /* Centraliza horizontalmente */
        box-shadow: rgb(20, 20, 20) 0 0 10px;
        /* Sombra azul ao redor do modal */
        transition: opacity 0.3s ease-in-out;
        /* Transição suave */
    }

    .custom-modal-content {
        position: relative;
        background-color: #fff;
        border: 1px solid rgba(0, 0, 0, 0.2);
        border-radius: 6px;
        /* Cantos arredondados como na imagem */
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
        width: 90%;
        max-width: 450px;
        /* Largura máxima para o modal */
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        /* Fonte padrão similar */
        display: flex;
        flex-direction: column;
    }

    .custom-modal-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1rem 1rem;
        border-bottom: 1px solid #dee2e6;
        border-top-left-radius: 5px;
        /* Arredondamento dos cantos superiores */
        border-top-right-radius: 5px;
    }

    .custom-modal-title {
        margin-bottom: 0;
        line-height: 1.5;
        font-size: 1.9rem;
        /* Tamanho da fonte do título */
        font-weight: 500;
        /* Peso da fonte do título */
        color: #333;
    }

    .custom-close-button {
        padding: 0.5rem 0.5rem;
        margin: -0.5rem -0.5rem -0.5rem auto;
        background-color: transparent;
        border: 0;
        font-size: 2.5rem;
        font-weight: 700;
        line-height: 1;
        color: #333;
        text-shadow: 0 1px 0 #fff;
        opacity: 0.5;
        cursor: pointer;
    }

    .custom-close-button:hover {
        opacity: 0.75;
    }

    .custom-modal-body {
        position: relative;
        flex: 1 1 auto;
        padding: 1rem;
        color: #333;
    }

    .custom-modal-body p {
        margin-top: 0;
        margin-bottom: 1rem;
        /* Espaço abaixo do texto "Mostrar colunas..." */
        font-size: 1.1rem;
        /* Tamanho da fonte do texto informativo */
        font-weight: bold;
        /* Negrito para destacar o texto */
    }

    .custom-modal-body .form-check {
        margin-bottom: 0.75rem;
        /* Espaço entre os checkboxes */
        display: flex !important;
        align-items: center !important;
    }

    .custom-modal-body .form-check-input {
        margin-right: 0.7rem;
        /* Espaço entre checkbox e label */
        width: 14px;
        height: 14px;
        cursor: pointer;
        margin-top: 0 !important;
        /* Remove o margin padrão do Bootstrap */
        /* Deixar os checkboxes um pouco mais arredondados */
        border-radius: 4px !important;
        /* Arredondar os cantos dos checkboxes */
    }

    .custom-modal-body .form-check-label {
        font-size: 1.3rem;
        /* Tamanho da fonte dos labels dos checkboxes */
        font-weight: 550;
        /* Peso da fonte dos labels dos checkboxes */
        color: #212529;
        cursor: pointer;
        line-height: 1.5;
        /* Alinha o texto do label com o checkbox */
        margin-bottom: 0;
        /* Remove o espaço abaixo do label */
        display: inline-block;
        /* Permite que o label seja clicável */
    }


    .custom-modal-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        /* Alinha botões à direita */
        padding: 0.75rem 1rem;
        border-top: 1px solid #dee2e6;
        border-bottom-right-radius: 5px;
        /* Arredondamento dos cantos inferiores */
        border-bottom-left-radius: 5px;
    }

    .custom-modal-footer .btn {
        padding: 0.4rem 1rem;
        font-size: 1.3rem;
        border-radius: 4px;
        /* Cantos arredondados dos botões */
        cursor: pointer;
        margin-left: 0.5rem;
        line-height: 1.5;
        margin-top: 0.8rem;
        /* Espaço acima dos botões */
    }

    .custom-modal-footer .btn-cancelar {
        color: #6c757d;
        /* Cor do texto do botão Cancelar */
        background-color: #fff;
        border: 1px solid #ced4da;
        /* Borda do botão Cancelar */
    }

    .custom-modal-footer .btn-cancelar:hover {
        background-color: #667799;
        border-color: #667799;
        /* Um tom mais escuro para o hover */
        color: #fff;
        /* Cor do texto do botão Cancelar no hover */
    }

    .custom-modal-footer .btn-exportar {
        color: #fff;
        background-color: #0C8CE9;
        /* Cor de fundo do botão Exportar (azul da imagem) */
        border: 1px solid #0C8CE9;
        /* Borda do botão Exportar */
    }

    .custom-modal-footer .btn-exportar:hover {
        background-color: #1a4269;
        /* Um tom mais escuro para o hover */
        border-color: #1a4269;
    }

    /* Estilo para quando o botão está carregando (se estiver usando Bootstrap JS) */
    .custom-modal-footer .btn-exportar.disabled,
    .custom-modal-footer .btn-exportar:disabled {
        opacity: 0.65;
    }
</style>

<script type="text/javascript">
    $(function() {
        $('.datetimepicker').datetimepicker({
            'format': 'DD/MM/YYYY',
            'locale': 'pt-BR'
        });
    });

    $(document).ready(function() {
        <?php
        $minha_variavel = $this->input->post('data_inicio_homologacao_modal');
        if (strpos($minha_variavel, '-') !== false) {
        ?>
            $('#data_inicio_homologacao_modal').val('<?php echo date('d/m/Y', strtotime(str_replace('-', '/', $this->input->post('data_inicio_homologacao_modal')))) ?>');
        <?php } ?>

        <?php
        $minha_variavel = $this->input->post('data_fim_homologacao_modal');
        if (strpos($minha_variavel, '-') !== false) {
        ?>
            $('#data_fim_homologacao_modal').val('<?php echo date('d/m/Y', strtotime(str_replace('-', '/', $this->input->post('data_fim_homologacao_modal')))) ?>');
        <?php } ?>
    });
</script>
