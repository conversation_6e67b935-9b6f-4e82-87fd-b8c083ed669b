<?php echo form_open('', array('class' => 'form-horizontal')) ?>
	
	<div class="page-header">
		<h2>
			Editar Perfil
		</h2>
	</div>

	<div class="form-group">
		<label for="input-razao_social" class="col-sm-2 text-right">Descrição</label>
		<div class="col-sm-10">
			<input type="text" class="form-control" name="descricao" id="input-descricao" value="<?php echo set_value('descricao', $entry->descricao) ?>" placeholder="Descrição">
		</div>
	</div>
	
	<div class="form-group">
		<label class="col-sm-2 text-right">Permissões</label>
		<div class="col-sm-10">
			<div class="row">
		
				<?php foreach ($permissoes as $permissao) { ?>
					<?php if ($hasOwner && !$permissao->pagina) : ?>

						<div class="col-xs-10 col-md-4"><input <?php echo set_checkbox('permissao[]', $permissao->id_permissao, in_array($permissao->id_permissao, $related_perms) ) ?> type="checkbox" id="permissao<?php echo $permissao->id_permissao ?>" name="permissao[]" value="<?php echo $permissao->id_permissao ?>"> <label for="permissao<?php echo $permissao->id_permissao ?>"><?php echo $permissao->descricao ?></label></div>

					<?php elseif (!$permissao->pagina && !$permissao->has_owner) : ?>

						<div class="col-xs-10 col-md-4"><input <?php echo set_checkbox('permissao[]', $permissao->id_permissao, in_array($permissao->id_permissao, $related_perms) ) ?> type="checkbox" id="permissao<?php echo $permissao->id_permissao ?>" name="permissao[]" value="<?php echo $permissao->id_permissao ?>"> <label for="permissao<?php echo $permissao->id_permissao ?>"><?php echo $permissao->descricao ?></label></div>
						
					<?php endif; ?>
					
				<?php } ?>
	  
			</div>
		</div>
	</div>

	<div class="form-group">
		<label class="col-sm-2 text-right">Páginas</label>
		<div class="col-sm-10">
			<div class="row">
		
			<?php foreach ($permissoes as $permissao) { ?>
				<?php if($permissao->pagina) : ?>
					<div class="col-xs-10 col-md-4" style="margin-bottom: 5px"><input <?php echo set_checkbox('permissao[]', $permissao->id_permissao, in_array($permissao->id_permissao, $related_perms) ) ?> type="checkbox" id="permissao<?php echo $permissao->id_permissao ?>" name="permissao[]" value="<?php echo $permissao->id_permissao ?>"> <label style="display: inline;" for="permissao<?php echo $permissao->id_permissao ?>"><?php echo $permissao->descricao ?></label></div>
				<?php endif; ?>
			<?php } ?>
	  
			</div>
		</div>
	</div>
		
	<div class="form-group">
		<div class="col-sm-offset-2 col-sm-10">
			<button type="submit" class="btn btn-primary" value="1" name="submit"><i class="glyphicon glyphicon-floppy-disk"></i> Salvar</button>
			<a href="<?php echo site_url("cadastros/perfil") ?>" class="btn">Cancelar</a>
		</div>
	</div>

</form>
