<script type="text/javascript">
	var item = {

		edit: function() {
			checked = $('input[type="checkbox"][name="item[]"]:checked');

			if (checked.length == 0) {
				swal('Atenção', 'Selecione um item para realizar a edição', 'warning');
				return false;
			}

			if (checked.length > 1) {
				swal('Atenção', 'Só é possível editar um item por vez', 'warning');
				return false;
			}

			item_id = $(checked).val();
			location.href = '<?php echo base_url() . "cadastros/usuario/editar/" ?>' + item_id;
		},

		remove: function() {
			checked = $('input[type=checkbox]:checked');

			if (checked.length == 0) {
				swal('Atenção', 'Primeiro você deve selecionar os usuários que deseja excluir.', 'warning');
				return false;
			}

			var id_list = new Array();

			$('input[type="checkbox"][name="item[]"]:checked').each(function() {
				id_list.push($(this).val());
			});

			var post_data = {
				'id_list': id_list
			};

			swal({
                title: "Atenção!",
                text: "Você deseja excluir os registros selecionados?",
                type: "warning",
                confirmButtonText: "OK",
                cancelButtonText: "Cancelar",
                showConfirmButton: true,
                showCancelButton: true,
                allowOutsideClick: false
			}).then(function () {
				$.post('<?php echo site_url("cadastros/usuario/excluir") ?>',
					post_data,
					function(data, status, xhr) {
						var json = $.parseJSON(data);

						if (json.status == true) {
							window.location.reload();
						} else {
							swal('Atenção', "Ops... um erro aconteceu, recarregue a página.", 'warning');
						}
					}
				);
			});


		}
	};

	jQuery(document).ready(function() {
		// $('tbody').on('click', 'tr',function(e) {
		// 	if(!$(e.target).prop('href')) 
		//     {
		// 		var self = $(this);
		// 		var selectedLine = self.find('input[type="checkbox"]');
		// 		selectedLine.trigger("click");
		// 	}
		// });
		// $('tbody').on('click', 'input[type="checkbox"]', function(e)
		// {
		// 	if (e.clientX > 0 || e.clientY > 0)
		// 	{
		// 		e.stopPropagation();
		// 	}
		// });
		$('.click-select').on('click', function(e) {
			if (e.target.nodeName != 'INPUT') {
				$(this).find('input').click();
			}
		})
	});
</script>

<div class="page-header">
	<h2>
		Usuários

		<div class="col-md-10 pull-right">
			<div class="row">
				<?php echo form_open("", array('class' => 'form-horizontal', 'method' => 'get', 'name' => 'search_form', 'style' => 'padding-top: 2px;')); ?>

				<div class="form-group col-md-2" style="padding: 0; margin-right: 15px;">
					<input type="text" name="nome" placeholder="Nome de usuário" class="form-control" value="<?php echo set_value('nome', $nome); ?>">
				</div>

				<?php if (has_role('sysadmin') || has_role('consultor') || $checkMultiplasEmpresas) { ?>
					<div class="form-group col-md-3" style="padding: 0; margin-right: 15px; margin-left: 0;">
						<select class="form-control custom" name="id_empresa" id="select-empresa">
							<option value="">[Escolha a empresa]</option>
							<?php foreach ($empresas as $empresa) { ?>
								<option <?php echo set_select('id_empresa', $empresa->id_empresa, $empresa->id_empresa == $id_empresa) ?> value="<?php echo $empresa->id_empresa ?>"><?php echo $empresa->razao_social ?></option>
							<?php } ?>
						</select>
					</div>
				<?php } ?>

				<div class="form-group col-md-1" style="margin-top: -2px">
					<button class="btn btn-primary" type="submit"><i class="glyphicon glyphicon-search"></i></button>
				</div>

				<div class="form-group col-md-1" style="margin-top: -2px">
					<a href="<?php echo site_url("cadastros/usuario?reset_filter=1") ?>" class="btn btn-default">Limpar</a>
				</div>

				<?php echo form_close(); ?>

				<?php if (has_role("sysadmin") || has_role("gerenciar_usuarios")) : ?>
					<div class="form-group col-md-5 pull-right text-right" style="margin-top: -2px; padding-right: 0px;">
						<a href="<?php echo site_url("cadastros/usuario/novo") ?>" class="btn btn-default"><i class="glyphicon glyphicon-plus"></i> Novo usuário</a>
						<button onclick="item.edit()" class="btn btn-default"><i class="glyphicon glyphicon-edit"></i> Editar</button>
						<button onclick="item.remove()" class="btn btn-danger"><i class="glyphicon glyphicon-trash"></i> Excluir</button>
					</div>
				<?php endif; ?>
			</div>
		</div>
	</h2>
</div>

<?php if (isset($pagination)) { ?>
	<div class="controls">
		<div class="pull-right">
			<?php echo $pagination ?>
		</div>
	</div>
<?php } ?>

<table class="table table-striped table-hover" border="0">
	<thead>
		<tr>
			<?php if (has_role("sysadmin") || has_role("gerenciar_usuarios")) : ?>
				<th><input type="checkbox" id="toggle-checkbox" onclick="toggle_checkbox(this)" /></th>
			<?php endif; ?>

			<th>Nome / E-mail</th>
			<th>Empresa</th>
			<th>Perfil</th>
		</tr>
	</thead>
	<tbody>
		<?php

		if (isset($list) && count($list)) {
			foreach ($list as $item) {
		?>
				<tr class="click-select">
					<?php if (has_role("sysadmin") || has_role("gerenciar_usuarios")) : ?>
						<td><input type="checkbox" data-toggle="true" name="item[]" value="<?php echo $item->id_usuario ?>" /></td>
					<?php endif; ?>
					
					<td>
						<?php echo $item->nome ?> <br />
						<small><?php echo auto_link($item->email) ?></small>
					</td>
					<td><?php echo $item->empresa ?></td>
					<td><?php echo $item->perfil ?></td>
				</tr>
		<?php
			}
		}

		?>
	</tbody>
</table>

<?php if (isset($pagination)) { ?>
	<div class="controls">
		<div class="pull-right">
			<?php echo $pagination ?>
		</div>
	</div>
<?php } ?>