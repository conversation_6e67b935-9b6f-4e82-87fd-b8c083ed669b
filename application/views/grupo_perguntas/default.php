<?php 
    $nome = $this->input->post('nome');
    $ncm = $this->input->post('ncm');
?>

<div class="page-header">
    <h2>Grupo de Perguntas</h2>
</div>

<div class="row">
    <div class="col-md-6">
        <form action="<?php echo site_url("grupo_perguntas/index") ?>" accept-charset="utf-8" class="form-horizontal" method="post" name="search_form">
            <div class="row">
                <div class="col-md-4">
                    <input type="text" name="nome" placeholder="Nome do Grupo" class="form-control" value="<?php echo !empty($nome) ? $nome : ''; ?>">
                </div>

                <div class="col-md-4 pl-0">
                    <input type="number" name="ncm" placeholder="NCM do Grupo" class="form-control" value="<?php echo !empty($ncm) ? $ncm : ''; ?>">
                </div>

                <button class="btn btn-primary" type="submit"><i class="glyphicon glyphicon-search"></i></button>
                <a href="<?php echo site_url("grupo_perguntas/index?unsetFilters=1") ?>" class="btn btn-default">Limpar</a>
            </div>
        </form>
    </div>

    <div class="form-group col-md-6 pull-right text-right">
        <a href="<?php echo site_url("grupo_perguntas/novo") ?>" class="btn btn-default"><i class="glyphicon glyphicon-plus"></i> Novo Grupo de Perguntas</a>
        <button onclick="edit()" class="btn btn-default btn-editar"><i class="glyphicon glyphicon-edit"></i> Editar</button>

        <form action="<?php echo site_url("grupo_perguntas/delete") ?>" accept-charset="utf-8" class="form-horizontal d-inline" method="post" name="deleteForm" id="deleteForm">
            <button type="submit" class="btn btn-danger btn-excluir"><i class="glyphicon glyphicon-trash"></i> Excluir</button>
        </form>
    </div>
</div>

<?php if ($this->session->flashdata('deleted') == "true"): ?>
    <div class="alert alert-success" role="alert">
        Grupo(s) excluído(s) com sucesso!
    </div>
<?php elseif ($this->session->flashdata('deleted') == "false") : ?>
    <div class="alert alert-danger" role="alert">
        Algo inesperado aconteceu ao tentar excluir o(s) grupo(s). Contate um administrador.
    </div>
<?php endif; ?>

<?php if (isset($pagination)): ?>
	<div class="controls">
		<div class="pull-right">
			<?php echo $pagination ?>
		</div>
	</div>
<?php endif; ?>

<table class="table table-striped table-hover" border="0">
	<thead>
		<tr>
			<th><input type="checkbox" id="toggle-checkbox" onclick="toggle_checkbox(this)" /></th>
			<th width="30%">Nome</th>
			<th width="25%">NCM</th>
			<th width="25%">Criado por</th>
			<th width="20%">Atualizado em</th>
		</tr>
	</thead>
	<tbody>
        <?php if(!empty($gruposPerguntas)): ?>
            <?php foreach($gruposPerguntas as $grupoPergunta): ?>
                <tr class="click-select">
                    <td><input type="checkbox" data-toggle="true" name="grupoPergunta[]" value="<?php echo $grupoPergunta->id ?>" form="deleteForm"/></td>
                    <td><a href="<?php echo site_url("grupo_perguntas/editar/{$grupoPergunta->id}") ?>"><?php echo $grupoPergunta->nome; ?></a></td>
                    <td><?php echo !empty($grupoPergunta->ncm) ? $grupoPergunta->ncm : "-"; ?></td>
                    <td><?php echo $grupoPergunta->nome_usuario; ?></td>
                    <td><?php echo date('d/m/Y H:i', strtotime($grupoPergunta->atualizado_em)); ?></td>
                </tr>
            <?php endforeach; ?>
        <?php else: ?>
            <tr class="click-select">
                <td colspan="5">Nenhum registro foi encontrado.</td>
            </tr>
        <?php endif; ?>
	</tbody>
</table>

<?php if (isset($pagination)): ?>
	<div class="controls">
		<div class="pull-right">
			<?php echo $pagination ?>
		</div>
	</div>
<?php endif; ?>

<style type="text/css">
    .pl-0 {
        padding-left: 0px;
    } 

    .d-inline {
        display: inline;
    }
</style>

<script type="text/javascript">
    $(function() {
        $('.btn-editar').hide();
        $('.btn-excluir').hide();

        $('[name="grupoPergunta[]"]').on('change', function() {
            let checkedGroups = $('[name="grupoPergunta[]"]:checked').length;
            
            if (checkedGroups == 0) {
                $('.btn-excluir').hide();
            }

            if (checkedGroups > 1 || checkedGroups == 0) {    
                return $('.btn-editar').hide();
            }
            
            $('.btn-editar').show();
            $('.btn-excluir').show();
        });

		$('.click-select').on('click', function(e) {
			if (e.target.nodeName != 'INPUT') {
				$(this).find('input').click();
			}
		})
    })

    function edit() {
        let idGrupo = $('[name="grupoPergunta[]"]:checked').val();

        location.href = `<?php echo base_url("grupo_perguntas/editar"); ?>/${idGrupo}`;
    }
</script>