<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

require_once  APPPATH . 'libraries/Spout/Autoloader/autoload.php';

use Box\Spout\Reader\ReaderFactory;
use Box\Spout\Common\Type;

class Uploadnext extends MY_Controller
{

    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }

        if (!has_role('arquivos_homologacao')) {
            show_permission();
        }

        $this->load->library('breadcrumbs');
        $this->load->helper('formatador_helper');
    }

    public function functionShutdown($msg_function)
    {
        $filename = FCPATH . 'assets/logs/log.txt';
        $file = false;
        if (file_exists($filename) && is_writable($filename)) {
            try {
                chmod($filename, 0777);
            } catch (\Throwable $th) {
                return;
            }
            $file = fopen($filename, "a");
        }

        if ($file !== false) { // Verifica se o arquivo foi aberto com sucesso
            try {
                fwrite($file, $msg_function . "\n");
            } catch (\Throwable $th) {
                return;
            } finally {
                fclose($file); // Fecha o arquivo
            }
        }
    }

    public function index()
    {
        set_time_limit(900);

        $data = array();
        $data['processamento'] = null;
        $this->load->model(array(
            'empresa_model'
        ));
        $this->load->helper('formatador_helper');

        $data['empresas'] = $this->empresa_model->get_all_entries();

        $empresa = $this->empresa_model->get_entry(sess_user_company());

        $campos_adicionais_empresa = explode('|', $empresa->campos_adicionais);

        $hasDescricaoGlobal = in_array('descricao_global', $campos_adicionais_empresa);

        if ($hasDescricaoGlobal) {
            $arquivo_modelo = base_url('assets/excel_modelo/itens-para-homologacao_com_descricao_global.xlsx');
        } else {
            $arquivo_modelo = base_url('assets/excel_modelo/itens-para-homologacao.xlsx');
        }

        $data['arquivo_modelo'] = $arquivo_modelo;

        if ($this->input->post('submit')) {
            $inicio = microtime(true);
            $upload_path = config_item('upload_tmp_path');

            $this->load->library('form_validation');

            $this->form_validation->set_rules('id_empresa', 'Empresa', 'trim|required');

            if ($this->form_validation->run() == true) {
                $id_empresa = $this->input->post('id_empresa');
                $validar    = (bool) $this->input->post('validar');

                $funcoes_adicionais = explode('|', $empresa->funcoes_adicionais);
                $upload_path = config_item('upload_tmp_path');
                
                $config = [
                    'upload_path' => $upload_path,
                    'allowed_types' => 'xlsx',
                    'max_size' => 2147483648
                ];

                $this->load->library('unzip');
                $this->load->service('UploadService');
                $uploadResult = $this->uploadservice->doUpload($config);

                if (!$uploadResult['success']) {
                    $processamento = [
                        'status' => 'falha_upload',
                        'message' => 'Não foi possível realizar o upload do arquivo, planilha fora do padrão compatível!',
                        'details' => $uploadResult['errors']
                    ];
                    $this->session->set_flashdata('processamento', $processamento);
                    redirect('/uploadnext/');
                    return;
                }

                $upload_data = $uploadResult['data'];

                $file_ext = strtolower($upload_data['file_ext']);

                // xlsx file
                if ($file_ext == '.xlsx') {
                    $logs = [
                        'inseridos' => [],
                        'atualizados' => [],
                        'nao_atualizados' => [],
                        'com_erro' => [],
                        'fotos_atualizadas' => []
                    ];

                    $msg = "Data: " . date('d/m/Y \à\s H:i:s') . " uploadnext - INICIO " . " Empresa: " . sess_user_company() . " Usuario: " . sess_user_id() . " Nome do arquivo: " . $upload_data['file_name'] . " local: " . $upload_data['full_path'];
                    register_shutdown_function([$this, 'functionShutdown'], $msg);

                    $this->load->helper('text_helper');

                    $columnMapping = $this->getColumnMapping();

                    $this->load->service('SpreadsheetService');
                    $result = $this->spreadsheetservice
                        ->readHomologationSpreadsheet(
                            $upload_data['full_path'], 
                            $columnMapping
                        );

                    $planilha_dados = $result['data'];
                    $idx = $result['idx'];
                    $colunas_originais = $result['colunas_originais'];

                    if (!empty($result['errors'])) {
                        $logs['com_erro'] = array_merge(
                            $logs['com_erro'],
                            ['planilha_errors' => $result['errors']]
                        );
                    }

                    $count_register = 0;
                    foreach ($planilha_dados as $item) {
                        $count_register++;
                        $row = $item['row'];
                        $linha_planilha = $item['line_number'];

                        $count_register++;

                        // Processa os dados da linha (validação de vazio + limpeza)
                        if ($this->_processRowData($row)) {
                            continue;
                        }

                        // Validação da linha (verificar campos obrigatórios, etc.)
                        $erros = $this->validarLinhaPlanilha(
                            $row,
                            $idx,
                            $colunas_originais,
                            $linha_planilha,
                            $empresa
                        );

                        if (!empty($erros)) {
                            foreach ($erros as $erro) {
                                $logs['com_erro'][] = $erro;
                            }
                            continue;
                        }

                        // Processamento da linha via service
                        $this->load->service('CadItemImportService');
                        $result = $this->caditemimportservice->processRow(
                            $row,
                            $idx,
                            $colunas_originais,
                            $linha_planilha,
                            $empresa,
                            $funcoes_adicionais,
                            $validar,
                            $logs
                        );
                    }

                    // Formata os logs para exibição
                    $this->load->service('LogService');
                    $logResult = $this->logservice->formatLog(
                        $logs['inseridos'],
                        $logs['atualizados'],
                        $logs['nao_atualizados'],
                        $logs['com_erro'],
                        $logs['fotos_atualizadas'],
                        $upload_data['orig_name']
                    );

                    // Gerar relatório detalhado se houver erros
                    $this->load->service('ReportService');
                    $report_url = $this->_generateErrorReport($logs, $upload_data);

                    // Calcular o total de erros corretamente
                    $total_erros = 0;
                    foreach ($logs['com_erro'] as $key => $value) {
                        if ($key === 'planilha_errors') {
                            $total_erros += count($value);
                        } else {
                            $total_erros++;
                        }
                    }

                    // Preparar dados para o modal de resultado
                    $total_processado = count($logs['inseridos']) + count($logs['atualizados']) + $total_erros;
                    
                    $processamento = [
                        'total' => $total_processado,
                        'sucesso' => count($logs['inseridos']) + count($logs['atualizados']),
                        'inseridos' => count($logs['inseridos']),
                        'atualizados' => count($logs['atualizados']),
                        'erro' => $total_erros,
                        'report_url' => $report_url,
                        'message' => $logResult['message'],
                        'type' => $logResult['type']
                    ];

                    // Salvar na sessão para exibir após o redirect
                    $this->session->set_flashdata('processamento', $processamento);

                    // Calcula os valores de inseridos e atualizados antes de montar a string
                    $inseridos = $logs['inseridos'] > 0 ? count($logs['inseridos']) : '0';
                    $atualizados = $logs['atualizados'] > 0 ? count($logs['atualizados']) : '0';

                    // Monta a string da mensagem usando as variáveis calculadas
                    $mensagem = "Importação finalizada. Sucesso: {$processamento['sucesso']}, Inseridos: {$inseridos}, Atualizados: {$atualizados}, Erros: {$processamento['erro']}.";

                    // Define o tipo de mensagem com base no número de erros
                    $tipo = $processamento['erro'] > 0 ? 'warning' : 'success';

                    $this->message_next_render($mensagem, $tipo);
                    
                }

                unlink($upload_data['full_path']);
                $fim = microtime(true);
                $msg = "Data: " . date('d/m/Y \à\s H:i:s') . " uploadnext - Concluído - Tempo de Processamento: " . number_format(($fim - $inicio), 2)  . " Empresa: " . sess_user_company() . " Usuario: " . sess_user_id();
                $this->functionShutdown($msg);
                redirect('/uploadnext/');
            } else {
                $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
                $this->message_on_render($err, 'error');
            }
        }

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Enviar itens para homologação', '/uploadnext/');

        $this->render('uploadnext', $data);
    }

    public function atualizar_status($item_base, $part_number, $estabelecimento, $id_empresa, $forcar_mudanca = FALSE, $tipo = 'novos', $id_item = null, $homologar = FALSE, $novo_grupo_tarifario = FALSE)
    {
        if ($forcar_mudanca == FALSE && $tipo != 'novos') {
            // Não deve haver troca de status de itens já homologados ou reprovados
            if (($item_base->id_status == '2' || $item_base->id_status == '3') && $novo_grupo_tarifario == FALSE) {
                return;
            }

            if ($homologar == FALSE) {
                return;
            }
        }

        $this->load->library("Item/Status");

        $this->status->set_status("homologar");
        $this->status->update_item($part_number, $estabelecimento, $id_empresa);

        if ($tipo != 'novos' && !empty($id_item)) {
            $this->cad_item_homologacao_model->drop_item($id_item);
        }
    }

    public function validar_descricao_item()
    {
        $data = array();
        $can_formatar_texto = company_can("formatar_texto");

        if ($this->input->post('item')) {
            $this->load->model('cad_item_model');
            $this->load->model('item_log_model');
            $errors = $success = array();
            $itens                          = $this->input->post('item');
            $itens_descricao                = $this->input->post('item_descricao');

            foreach ($itens as $k => $id_item) {
                $item = $this->cad_item_model->get_entry($id_item);
                $nova_descricao_sugerida = formatar_texto($can_formatar_texto, $itens_descricao[$k]);

                $dbupdate = array(
                    'descricao_mercado_local'   => $nova_descricao_sugerida,
                    'houve_descricao_manual'    => 0
                );

                $motivo = "";

                if ($nova_descricao_sugerida != $item->descricao_mercado_local) {
                    $motivo = "Alteração da descrição proposta resumida: " . $item->descricao_mercado_local . " -> <strong>" . $nova_descricao_sugerida . "</strong><br />";
                }

                if (!empty($motivo)) {
                    if ($this->cad_item_model->update_item($item->part_number, $item->id_empresa, $dbupdate, $motivo, $item->estabelecimento)) {
                        $success[] = $item->part_number;
                    } else {
                        $errors[] = $item->part_number;
                    }
                }
            }

            if (count($errors) > 0) {
                $error_message = '<strong>Erro!</strong> Não foi possível atualizar os seguintes itens: ' . implode(', ', $errors) . '';
                echo '<div class="alert alert-danger">' . $error_message . '</div>';
            }

            if (count($success) > 0) {
                $success_message = '<strong>Sucesso!</strong> Os seguintes foram atualizados com sucesso: ' . implode(', ', $success) . '';
                echo '<div class="alert alert-success">' . $success_message . '</div>';
            }

            return TRUE;
        }

        $list_arr = $this->session->userdata('validar-dados-itens');

        if (empty($list_arr) || count($list_arr) == 0) {
            show_404();
        }

        $data['list'] = $list_arr;

        $this->load->view('uploadnext-validar-dados-itens-modal', $data);
    }

    public function validar_evento()
    {
        $data = array();
        if ($this->input->post('id_item')) {
            $this->load->model('cad_item_model');
            $this->load->model('item_model');
            $this->load->model('item_log_model');
            $errors = $success = array();
            $itens        = $this->input->post('id_item');
            $itens_evento = $this->input->post('item_evento');

            foreach ($itens as $k => $id_item) {
                $cad_item = $this->cad_item_model->get_entry($id_item);
                $item = $this->item_model->get_entry($cad_item->part_number, $cad_item->id_empresa, $cad_item->estabelecimento);

                $novo_evento = $itens_evento[$id_item];

                $dbupdate = array(
                    'evento'   => $novo_evento,
                );

                $motivo = "";

                if ($novo_evento != $item->evento) {
                    $motivo = "Alteração de evento: " . $item->evento . " -> <strong>" . $novo_evento . "</strong><br />";
                }

                if (!empty($motivo)) {
                    if ($this->item_model->update_item($item->part_number, $item->id_empresa, $dbupdate, $motivo, $item->estabelecimento)) {
                        $success[] = $item->part_number;
                    } else {
                        $errors[] = $item->part_number;
                    }
                }
            }

            if (count($errors) == 0 && count($success) == 0) {
                $info_message = '<strong>Hmmm..</strong> Parece que os itens selecionados já estão atualizados.';
                echo '<div class="alert alert-info">' . $info_message . '</div>';

                return true;
            }

            if (count($errors) > 0) {
                $error_message = '<strong>Erro!</strong> Não foi possível atualizar os seguintes itens: ' . implode(', ', $errors) . '';
                echo '<div class="alert alert-danger">' . $error_message . '</div>';

                return true;
            }

            if (count($success) > 0) {
                $success_message = '<strong>Sucesso!</strong> Os seguintes foram atualizados com sucesso: ' . implode(', ', $success) . '';
                echo '<div class="alert alert-success">' . $success_message . '</div>';

                return true;
            }

            return false;
        }

        $list_arr = $this->session->userdata('validar-evento');

        if (empty($list_arr) || count($list_arr) == 0) {
            show_404();
        }

        $data['list'] = $list_arr;

        $this->load->view('uploadnext-validar-evento', $data);
    }

    public function validar_funcao_aplicacao_marca()
    {
        $data = array();

        if ($this->input->post('item')) {
            $this->load->model('cad_item_model');
            $this->load->model('item_log_model');
            $this->load->model('empresa_model');

            $errors = $success = array();

            $itens = $this->input->post('item');

            $id_empresa = $this->input->post('id_empresa');
            $empresa = $this->empresa_model->get_entry($id_empresa);

            $campos_adicionais = explode('|', $empresa->campos_adicionais);

            $has_funcao = FALSE;
            $has_inf_adicionais = FALSE;
            $has_aplicacao = FALSE;
            $has_marca = FALSE;
            $has_material_constitutivo = FALSE;
            $has_descricao_proposta_completa = FALSE;

            if (in_array('funcao', $campos_adicionais)) {
                $has_funcao = TRUE;
                $itens_funcao = $this->input->post('item_funcao');
            }

            if (in_array('inf_adicionais', $campos_adicionais)) {
                $has_inf_adicionais = TRUE;
                $itens_inf_adicionais = $this->input->post('item_inf_adicionais');
            }

            if (in_array('aplicacao', $campos_adicionais)) {
                $has_aplicacao = TRUE;
                $itens_aplicacao = $this->input->post('item_aplicacao');
            }

            if (in_array('marca', $campos_adicionais)) {
                $has_marca = TRUE;
                $itens_marca = $this->input->post('item_marca');
            }

            if (in_array('material_constitutivo', $campos_adicionais)) {
                $has_material_constitutivo = TRUE;
                $itens_material_constitutivo = $this->input->post('item_material_constitutivo');
            }

            if (in_array('descricao_proposta_completa', $campos_adicionais)) {
                $has_descricao_proposta_completa = TRUE;
                $itens_descricao_completa = $this->input->post('item_descricao_completa');
            }

            foreach ($itens as $k => $id_item) {
                $item = $this->cad_item_model->get_entry($id_item);

                $motivo = "";
                $dbupdate = array();

                if ($has_funcao === TRUE && isset($itens_funcao[$k])) {
                    $nova_funcao = $itens_funcao[$k];

                    if ($nova_funcao != $item->funcao) {
                        $dbupdate['funcao'] = $nova_funcao;
                        $dbupdate['houve_funcao_manual'] = 0;

                        $funcao_texto = $item->funcao ? $item->funcao : '<em>Não informado</em>';
                        $nova_funcao_texto = $nova_funcao ? $nova_funcao : '<em>Não informado</em>';

                        $motivo .= "Alteração da função: " . $funcao_texto . " -> <strong>" . $nova_funcao_texto . "</strong><br />";
                    }
                }

                if ($has_inf_adicionais === TRUE && isset($itens_inf_adicionais[$k])) {
                    $nova_inf_adicionais = $itens_inf_adicionais[$k];

                    if ($nova_inf_adicionais != $item->inf_adicionais) {
                        $dbupdate['inf_adicionais'] = $nova_inf_adicionais;
                        $dbupdate['houve_inf_adicionais_mnual'] = 0;

                        $inf_adicionais_texto = $item->inf_adicionais ? $item->inf_adicionais : '<em>Não informado</em>';
                        $nova_inf_adicionais_texto = $nova_inf_adicionais ? $nova_inf_adicionais : '<em>Não informado</em>';

                        $motivo .= "Alteração das Informações Adicionais: " . $inf_adicionais_texto . " -> <strong>" . $nova_inf_adicionais_texto . "</strong><br/>";
                    }
                }

                if ($has_aplicacao === TRUE && isset($itens_aplicacao[$k])) {
                    $nova_aplicacao = $itens_aplicacao[$k];

                    if ($nova_aplicacao != $item->aplicacao) {
                        $dbupdate['aplicacao'] = $nova_aplicacao;
                        $dbupdate['houve_aplicacao_manual'] = 0;

                        $aplicacao_texto = $item->aplicacao ? $item->aplicacao : '<em>Não informado</em>';
                        $nova_aplicacao_texto = $nova_aplicacao ? $nova_aplicacao : '<em>Não informado</em>';

                        $motivo .= "Alteração da aplicação: " . $aplicacao_texto . " -> <strong>" . $nova_aplicacao_texto . "</strong><br />";
                    }
                }

                if ($has_marca === TRUE && isset($itens_marca[$k])) {
                    $nova_marca = $itens_marca[$k];

                    if ($nova_marca != $item->marca) {
                        $dbupdate['marca'] = $nova_marca;
                        $dbupdate['houve_marca_manual'] = 0;

                        $marca_texto = $item->marca ? $item->marca : '<em>Não informado</em>';
                        $nova_marca_texto = $nova_marca ? $nova_marca : '<em>Não informado</em>';

                        $motivo .= "Alteração da marca: " . $marca_texto . " -> <strong>" . $nova_marca_texto . "</strong><br />";
                    }
                }

                if ($has_material_constitutivo === TRUE && isset($itens_material_constitutivo[$k])) {
                    $novo_material_constitutivo = $itens_material_constitutivo[$k];

                    if ($novo_material_constitutivo != $item->material_constitutivo) {
                        $dbupdate['material_constitutivo'] = $novo_material_constitutivo;
                        $dbupdate['houve_material_constitutivo_manual'] = 0;

                        $material_constitutivo_texto = $item->material_constitutivo ? $item->material_constitutivo : '<em>Não informado</em>';
                        $novo_material_constitutivo_texto = $novo_material_constitutivo ? $novo_material_constitutivo : '<em>Não informado</em>';

                        $motivo .= "Alteração do material constitutivo: " . $material_constitutivo_texto . " -> <strong>" . $novo_material_constitutivo_texto . "</strong><br />";
                    }
                }

                if ($has_descricao_proposta_completa === TRUE && isset($itens_descricao_completa[$k])) {
                    $nova_descricao_completa = $itens_descricao_completa[$k];

                    if ($nova_descricao_completa != $item->descricao_proposta_completa) {
                        $dbupdate_item['descricao_proposta_completa'] = $nova_descricao_completa;
                        $dbupdate_item['houve_descricao_completa_manual'] = 0;

                        $descricao_completa_texto = $item->descricao_proposta_completa ? $item->descricao_proposta_completa : '<em>Não informado</em>';
                        $nova_descricao_completa_texto = $nova_descricao_completa ? $nova_descricao_completa : '<em>Não informado</em>';

                        $motivo_item = "Alteração da Descrição proposta completa: " . $descricao_completa_texto . " -> <strong>" . $nova_descricao_completa_texto . "</strong><br />";

                        $this->load->model('item_model');
                        $dbdata_item_ret = $this->item_model->update_item($item->part_number, $item->id_empresa, $dbupdate_item, $motivo_item, $item->estabelecimento);
                    }
                }

                if (!empty($motivo)) {
                    if (!empty($dbupdate)) {
                        $dbupdate_ret = $this->cad_item_model->update_item($item->part_number, $item->id_empresa, $dbupdate, $motivo, $item->estabelecimento);
                    }
                }

                if (isset($dbdata_item_ret) || isset($dbupdate_ret)) {
                    $success[] = $item->part_number;
                } else {
                    $errors[] = $item->part_number;
                }
            }

            if (count($errors) > 0) {
                $error_message = '<strong>Erro!</strong> Não foi possível atualizar os seguintes itens: ' . implode(', ', $errors) . '';
                echo '<div class="alert alert-danger">' . $error_message . '</div>';
            }

            if (count($success) > 0) {
                $success_message = '<strong>Sucesso!</strong> Os seguintes foram atualizados com sucesso: ' . implode(', ', $success) . '';
                echo '<div class="alert alert-success">' . $success_message . '</div>';
            }

            return TRUE;
        }

        $list_arr = $this->session->userdata('validar-funcao-aplicacao-marca');

        if (empty($list_arr) || count($list_arr) == 0) {
            show_404();
        }

        $data['list'] = $list_arr;

        $this->load->view('uploadnext-validar-funcao-aplicacao-marca-modal', $data);
    }

    public function validar_dados_itens()
    {
        $data = array();
        $can_formatar_texto = company_can("formatar_texto");

        if ($this->input->post('item')) {
            $this->load->model('cad_item_model');
            $this->load->model('item_log_model');
            $errors = $success = array();
            $itens                          = $this->input->post('item');
            $itens_descricao                = $this->input->post('item_descricao');
            $itens_subsidio                 = $this->input->post('item_subsidio');
            $itens_caracteristicas          = $this->input->post('item_caracteristicas');
            $itens_memoria_classificacao    = $this->input->post('item_memoria_classificacao');

            foreach ($itens as $k => $id_item) {
                $item = $this->cad_item_model->get_entry($id_item);
                $nova_descricao_sugerida =  formatar_texto($can_formatar_texto, $itens_descricao[$k]);
                $novo_subsidio = $itens_subsidio[$k];
                $nova_caracteristicas = $itens_caracteristicas[$k];
                $nova_memoria_classificacao = $itens_memoria_classificacao[$k];

                $dbupdate = array(
                    'descricao_mercado_local'   => $nova_descricao_sugerida,
                    'houve_descricao_manual'    => 0
                );

                if ($nova_descricao_sugerida !== $item->descricao_mercado_local) {
                    $motivo = "Descrição: " . $item->descricao_mercado_local . " -> " . $nova_descricao_sugerida . "<br />";
                }

                if ($novo_subsidio !== $item->subsidio) {
                    $dbupdate['subsidio'] = $novo_subsidio;
                    $motivo .= "Subsídio: " . $item->subsidio . " -> " . $novo_subsidio . "<br />";
                }

                if ($nova_caracteristicas !== $item->caracteristicas) {
                    $dbupdate['caracteristicas'] = $nova_caracteristicas;
                    $motivo .= "Características: " . $item->caracteristicas . " -> " . $nova_caracteristicas . "<br />";
                }

                if ($nova_memoria_classificacao !== $item->memoria_classificacao) {
                    $dbupdate['memoria_classificacao'] = $nova_memoria_classificacao;
                    $motivo .= "Memória de Classificação: " . $item->memoria_classificacao . " -> " . $nova_memoria_classificacao;
                }

                if ($this->cad_item_model->update_item($item->part_number, $item->id_empresa, $dbupdate, $motivo, $item->estabelecimento)) {
                    $success[] = $item->part_number;
                } else {
                    $errors[] = $item->part_number;
                }
            }

            if (count($errors) > 0) {
                $error_message = '<strong>Erro!</strong> Não foi possível atualizar os seguintes itens: ' . implode(', ', $errors) . '';
                echo '<div class="alert alert-danger">' . $error_message . '</div>';
            }

            if (count($success) > 0) {
                $success_message = '<strong>Sucesso!</strong> Os seguintes foram atualizados com sucesso: ' . implode(', ', $success) . '';
                echo '<div class="alert alert-success">' . $success_message . '</div>';
            }

            return TRUE;
        }

        $list_arr = $this->session->userdata('validar-dados-itens');

        if (empty($list_arr) || count($list_arr) == 0) {
            show_404();
        }

        $data['list'] = $list_arr;

        $this->load->view('uploadnext-validar-dados-itens-modal', $data);
    }

    public function validar_grupo_tarifario_itens()
    {
        $data = array();

        if ($this->input->post('item')) {
            $this->load->model('cad_item_model');
            $this->load->model('item_log_model');

            $errors = $success = array();

            $itens = $this->input->post('item');
            $itens_novo_grupo_tarifario  = $this->input->post('item_novo_grupo_tarifario');

            foreach ($itens as $k => $id_item) {
                $item = $this->cad_item_model->get_entry($id_item);
                $id_grupo_tarifario = $itens_novo_grupo_tarifario[$k];
                $dbupdate = array(
                    'id_grupo_tarifario' => $id_grupo_tarifario
                );

                if ($this->cad_item_model->update_item($item->part_number, $item->id_empresa, $dbupdate, $item->estabelecimento)) {
                    $success[] = $item->part_number;
                } else {
                    $errors[] = $item->part_number;
                }
            }

            if (count($errors) > 0) {
                $error_message = '<strong>Erro!</strong> Não foi possível atualizar os seguintes itens: ' . implode(', ', $errors) . '';
            }

            if (count($success) > 0) {
                $success_message = '<strong>Sucesso!</strong> Os seguintes foram atualizados com sucesso: ' . implode(', ', $success) . '';
            }

            echo '<div class="alert alert-success">' . $success_message . '</div>';

            return TRUE;
        }

        $list_arr = $this->session->userdata('validar-grupo-tarifario-itens');

        if (empty($list_arr) || count($list_arr) == 0) {
            show_404();
        }

        $data['list'] = $list_arr;
        $this->load->view('uploadnext-validar-grupo-tarifario-itens-modal', $data);
    }

    private function getColumnMapping()
    {
        return [
            'part_number'                   => ['PN', 'Codigo', 'Codigo\s(do\s)?(Item|Produto)', 'Part Number'],
            'estabelecimento'               => ['Estab(\.)?', 'Estabelecimento', 'ESTABELECIMENTO'],
            'observacoes'                   => ['observacoes', 'observacões', 'OBSERVACOES', 'OBSERVACÕES'],
            'descricao_proposta_resumida'   => ['Desc(\.|ricao)?\sProposta\sResumida'],
            'descricao_proposta_completa'   => ['Desc(\.|ricao)?\sProposta\sCompleta'],
            'grupo_tarifario'               => ['GRUPO', 'Grupo', 'Grupo\sTarifario'],
            'caracteristicas'               => ['Caracteristica(s)?'],
            'subsidio'                      => ['Subsidio'],
            'id_resp_fiscal'                => ['Fiscal', 'Resp(\.|onsavel)?\sFiscal'],
            'id_resp_engenharia'            => ['(Engenheiro|Tecnico)', 'Resp(\.|onsavel)?\s(Tecnico|Engenharia|Engenheiro)'],
            'forcar_atualizacao'            => ['Forcar', 'Atualizar', 'Forcar\sAtualizacao(\s\(Sim\/Nao\))?', 'Atualizar(\s\(Sim\/Nao\))?', 'ATUALIZAR (\s\(Sim\/Nao\))?'],
            'memoria_classificacao'         => ['Memoria', 'Memoria\s(de\s)?Classificacao'],
            'cnpj'                          => ['CNPJ', 'Empresa'],
            'evento'                        => ['Evento'],
            'dispositivos_legais'           => ['Dispositivo\sLegal', 'Dispositivos\sLegais'],
            'solucao_consulta'              => ['Solucao', 'Solucao\s(de\s)?Consulta'],
            'funcao'                        => ['Funcao'],
            'peso'                          => ['Peso'],
            'prioridade'                    => ['prioridade', 'Prioridade', 'PRIORIDADE'],
            'inf_adicionais'                => ['Informacoes Adicionais', 'Informações Adicionais', 'INFORMAÇÕES ADICIONAIS', 'INFORMACOES ADICIONAIS'],
            'aplicacao'                     => ['Aplicacao'],
            'marca'                         => ['Marca'],
            'material_constitutivo'         => ['Material', 'Material\sConstitutivo'],
            'cod_cest'                      => ['CEST', 'Codigo\sCEST', 'CEST\sProposto'],
            'status_simplus'                => ['(.*?)Simplus(.*?)'],
            'li'                            => ['LICENCIAMENTO NÃO AUTOMATICO', 'licenciamento nao automatico'],
            'li_orgao_anuente'              => ['ORGÃO ANUENTE', 'ORGAO ANUENTE'],
            'li_destaque'                   => ['DESTAQUE LI'],
            'antidumping'                   => ['ANTIDUMPING ATIVO'],
            'suframa_descricao'             => ['DESCRIÇÃO SUFRAMA', 'DESCRICAO SUFRAMA'],
            'suframa_codigo'                => ['CÓDIGO PRODUTO SUFRAMA', 'CODIGO PRODUTO SUFRAMA'],
            'ex_ii'                         => ['EX-II'],
            'texto_ex_ii'                   => ['TEXTO DO EX-II'],
            'ex_ipi'                        => ['EX-IPI'],
            'texto_ex_ipi'                  => ['TEXTO DO EX-IPI'],
            'nve_atributo_aa'               => ['NVE ATRIBUTO AA'],
            'nve_valor_aa'                  => ['NVE VALOR AA'],
            'nve_atributo_ab'               => ['NVE ATRIBUTO AB'],
            'nve_valor_ab'                  => ['NVE VALOR AB'],
            'nve_atributo_ac'               => ['NVE ATRIBUTO AC'],
            'nve_valor_ac'                  => ['NVE VALOR AC'],
            'nve_atributo_ad'               => ['NVE ATRIBUTO AD'],
            'nve_valor_ad'                  => ['NVE VALOR AD'],
            'nve_atributo_ae'               => ['NVE ATRIBUTO AE'],
            'nve_valor_ae'                  => ['NVE VALOR AE'],
            'nve_atributo_af'               => ['NVE ATRIBUTO AF'],
            'nve_valor_af'                  => ['NVE VALOR AF'],
            'nve_atributo_ag'               => ['NVE ATRIBUTO AG'],
            'nve_valor_ag'                  => ['NVE VALOR AG'],
            'nve_atributo_ah'               => ['NVE ATRIBUTO AH'],
            'nve_valor_ah'                  => ['NVE VALOR AH'],
            'nve_atributo_u'                => ['NVE ATRIBUTO U'],
            'nve_valor_u'                   => ['NVE VALOR U'],
            'nve_atributo_ue'               => ['NVE ATRIBUTO UE'],
            'nve_valor_ue'                  => ['NVE VALOR UE'],
            'lista_cliente'                 => ['FAZ PARTE DA LISTA CLIENTE'],
            'maquina'                       => ['maquina', 'Maquina', 'MAQUINA'],
            'origem'                        => ['origem', 'Origem', 'ORIGEM', 'ORIGEM (PAIS)'],
            'cod_owner'                     => ['OWNER', 'CODIGO DO OWNER', 'Owner'],
            'descricao_global'              => ['DESCRICAO_GLOBAL', 'Descrição Global', 'Desc(\.|ricao)?\sGlobal'],
            'forcar_homologacao'            => ['FORCAR HOMOLOGACAO', 'FORÇAR'],
            'gestao_mensal'                 => ['GESTAO_MENSAL', 'GESTAO MENSAL', 'gestao_mensal'],
            'importado'                     => ['IMPORTADO'],
        ];
    }

    private function validarLinhaPlanilha($row, $idx, $colunas_originais, $linha_planilha, $empresa)
    {
        $erros = [];

        // Exemplo: validação do part_number
        if (isset($idx['part_number'])) {
            $part_number = clean_str($row[$idx['part_number']], true);
        }

        // Exemplo: validação do estabelecimento
        if (isset($idx['estabelecimento'])) {
            $estabelecimento = $row[$idx['estabelecimento']];
            $estabelecimento = preg_replace('/[\xA0]/u', '', trim($estabelecimento));

            if (empty($estabelecimento)) {
                $estabelecimento = $empresa->estabelecimento_default;
            }

        } else {
            $estabelecimento = $empresa->estabelecimento_default;
        }

        $estabelecimento = convert_accented_characters($estabelecimento);

        if (empty($part_number)) {
            $erros[] = [
                'linha' => $linha_planilha,
                'coluna' => $colunas_originais['part_number'] ?? 'Part Number',
                'campo' => 'part_number',
                'mensagem' => "Part Number não informado na linha <b>" . $linha_planilha . "</b>",
                'part_number' => 'N/A',
                'estabelecimento' => (!empty($estabelecimento) ? $estabelecimento : 'N/A')
            ];
        }

        if (empty($estabelecimento)) {
            $erros[] = [
                'linha' => $linha_planilha,
                'coluna' => $colunas_originais['estabelecimento'] ?? 'Estabelecimento',
                'campo' => 'estabelecimento',
                'mensagem' => 'Estabelecimento vazio',
                'part_number' => (!empty($part_number) ? $part_number : 'N/A'),
                'estabelecimento' => 'N/A'
            ];
        }

        return $erros;
    }

    /**
     * Gera relatório de erros e retorna a URL para download
     * 
     * @param array $logs Logs de processamento
     * @param array $upload_data Dados do arquivo enviado
     * @param array $spreadsheetResult Resultado do processamento da planilha
     * @return string URL do relatório ou string vazia em caso de falha
     */
    private function _generateErrorReport($logs, $upload_data)
    {
        if (empty($logs['com_erro'])) {
            return '';
        }
        
        try {
            $report_filename = $this->reportservice->generateErrorReport(
                $logs,
                $upload_data['orig_name']
            );
            
            if (empty($report_filename)) {
                return '';
            }
            
            // Obter o diretório usado para salvar o relatório
            $report_dir = $this->session->userdata('last_report_directory');
            if (empty($report_dir)) {
                log_message('error', 'Diretório do relatório não encontrado na sessão');
                return '';
            }
            
            // Verificar se o diretório é acessível via web
            if ($this->_isWebAccessibleDirectory($report_dir)) {
                $web_path = substr($report_dir, strlen(FCPATH));
                return site_url($web_path . $report_filename);
            }
            
            // Se não for acessível via web, copiar para um diretório acessível
            return $this->_copyReportToAccessibleDirectory($report_dir, $report_filename);
            
        } catch (Exception $e) {
            log_message('error', 'Erro ao gerar relatório: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * Verifica se um diretório é acessível via web
     * 
     * @param string $directory Caminho do diretório
     * @return bool True se o diretório é acessível via web
     */
    private function _isWebAccessibleDirectory($directory)
    {
        return strpos($directory, FCPATH) === 0;
    }

    /**
     * Copia o relatório para um diretório acessível via web
     * 
     * @param string $source_dir Diretório de origem
     * @param string $filename Nome do arquivo
     * @return string URL do relatório ou string vazia em caso de falha
     */
    private function _copyReportToAccessibleDirectory($source_dir, $filename)
    {
        $this->load->helper('file');
        $accessible_dir = FCPATH . 'assets/tmp/';
        
        if (!ensure_directory($accessible_dir)) {
            log_message('error', 'Não foi possível garantir o diretório acessível: ' . $accessible_dir);
            return '';
        }
        
        $accessible_path = $accessible_dir . $filename;
        if (!copy($source_dir . $filename, $accessible_path)) {
            log_message('error', 'Falha ao copiar relatório para diretório acessível');
            return '';
        }
        
        return site_url('assets/tmp/' . $filename);
    }

    /**
     * Processa os dados da linha da planilha
     * Verifica se a linha está vazia e limpa caracteres especiais
     * 
     * @param array &$row Array da linha da planilha (passado por referência)
     * @return bool True se a linha deve ser ignorada (vazia), False se deve ser processada
     */
    private function _processRowData(&$row)
    {
        // Verifica se a linha está vazia
        $empty = true;
        for ($j = 0; $j <= count($row) - 1; $j++) {
            if (!empty($row[$j])) {
                $empty = false;
            }
        }

        // Se a linha estiver vazia, retorna true para indicar que deve ser ignorada
        if ($empty == true) {
            return true;
        }

        // Limpeza de Caracteres Especiais
        array_walk($row, function (&$v) {
            $v = clean_str($v);
        });

        // Retorna false indicando que a linha deve ser processada
        return false;
    }
}
