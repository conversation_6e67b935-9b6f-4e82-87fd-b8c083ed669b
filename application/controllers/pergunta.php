<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

class <PERSON>gunta extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }

        if (!has_role('sysadmin') && !has_role('consultor')) {
            show_permission();
        }

        $this->load->model(array(
            'ctr_pergunta_model',
            'ctr_grupo_pergunta_model',
            'ctr_anexo_pergunta_model'
        ));

        $this->ctr_pergunta_model->set_namespace('pergunta');
    }

    public function delete()
    {
        $post = get_axios_post(); 

        $idPergunta = $post['idPergunta']; 
        $idGrupo = $post['idGrupo']; 

        if (!empty($idPergunta) && !empty($idGrupo)) {
            $delete = $this->ctr_grupo_pergunta_model->delete($idPergunta, $idGrupo);

            $anexos = $this->ctr_anexo_pergunta_model->getEntriesByPergunta($idPergunta);
            $upload_perguntas_path = config_item('upload_perguntas_path')."{$idPergunta}/";

            foreach ($anexos as $anexo) {
                unlink($upload_perguntas_path . $anexo->nome);
            }

            $this->ctr_anexo_pergunta_model->deleteByPergunta($idPergunta);
            
            rmdir($upload_perguntas_path);
        }

        if ($delete) {
            $message = "Sucesso! A pergunta foi removida do grupo.";
        } else {
            $message = "Erro! Não foi possível remover a pergunta.";
        }

        return response_json(array('message' => $message));
    }

    public function editar()
    {
        $data['message'] = "";
        $data['error'] = false;
        
        $idPergunta = $this->input->post("idPergunta");
        $post = $this->input->post();
        try {
            if (!empty($post)) {
                $update = $this->ctr_pergunta_model->update($this->input->post("idPergunta"), array(
                    "pergunta" => $this->input->post("pergunta")
                ));
            }
        } catch (Exception $e) {
            $data['message'] = $e->getMessage;
            $data['error'] = true;
        }

        $count = count($_FILES['arquivos']['name']);

        if ($count >= 1 && $data['message'] == "") {
            $data['message'] = "A pergunta foi editada e os arquivos foram relacionados.";
            
            $upload_perguntas_path = config_item('upload_perguntas_path');

            if (!is_dir($upload_perguntas_path)) {
                $old = umask(0);
                mkdir($upload_perguntas_path, 0777);
                umask($old);
            }

            $upload_path = "{$upload_perguntas_path}{$idPergunta}/";

            if (!is_dir($upload_path)) {
                $old = umask(0);
                mkdir("{$upload_perguntas_path}{$idPergunta}/", 0777);
                umask($old);
            }                        

            $config['upload_path'] = $upload_path;
            $config['allowed_types'] = 'xlsx|jpg|jpeg|png|txt|pdf';

            $this->load->library('upload');

            try {
                for($i=0;$i<$count;$i++){
                    if (!empty($_FILES['arquivos']['name'][$i])) {
                        $_FILES['arquivo']['name']     = $_FILES['arquivos']['name'][$i];
                        $_FILES['arquivo']['type']     = $_FILES['arquivos']['type'][$i];
                        $_FILES['arquivo']['tmp_name'] = $_FILES['arquivos']['tmp_name'][$i];
                        $_FILES['arquivo']['error']    = $_FILES['arquivos']['error'][$i];
                        $_FILES['arquivo']['size']     = $_FILES['arquivos']['size'][$i];
                        
                        $this->upload->initialize($config);
    
                        if (!$this->upload->do_upload('arquivo')) {
                            throw new Exception($this->upload->display_errors());
                        } else {
                            $file_data = $this->upload->data();
                            $file_ext = strtolower($file_data['file_ext']);
                            $full_path = $file_data['full_path'];
                            $upload_perguntas_path = config_item('upload_perguntas_path');    

                            $this->ctr_anexo_pergunta_model->save(array(
                                "hash"        => hash("md5", $file_data['raw_name']) . $file_ext,
                                "nome"        => $file_data['file_name'],
                                "criado_em"   => date("Y-m-d H:i:s"),
                                "id_pergunta" => $idPergunta
                            ));
                            // unlink($upload_path . $file_data['file_name']);
                        }
                    }
                }
            } catch (Exception $e) {
                $data['message'] = $e->getMessage();
                $data['error'] = true;
            }
        }

        if ($data['message'] == "") {
            $data['message'] = "A pergunta foi editada.";
        }

        return response_json($data);
    }

    public function novo()
    {
        $data['message'] = "";
        $data['error'] = false;
        $idPergunta = 0;

        if (!empty($this->input->post())) {
            try {
                $idPergunta = $this->ctr_pergunta_model->save(array(
                    "pergunta"   => $this->input->post('pergunta'),
                    "id_empresa" => sess_user_company(),
                    "id_usuario" => sess_user_id()
                ));

                if (empty($idPergunta)) {
                    throw new Exception("Não foi possível cadastrar pergunta.");
                } else {
                    $data['message'] = "A pergunta foi cadastrada.";
                }
            } catch (Exception $e) {
                $data['message'] = $e->getMessage();
                $data['error'] = true;
            }

            $this->ctr_grupo_pergunta_model->save(array(
                'id_grupo'    => $this->input->post('idGrupo'),
                'id_pergunta' => $idPergunta
            ));
        }

        $count = count($_FILES['arquivos']['name']);

        if ($count >= 1 && $idPergunta != 0) {
            $data['message'] = "A pergunta juntamente com os arquivos foram registrados.";
            
            $upload_perguntas_path = config_item('upload_perguntas_path');

            if (!is_dir($upload_perguntas_path)) {
                $old = umask(0);
                mkdir($upload_perguntas_path, 0777);
                umask($old);
            }

            $upload_path = "{$upload_perguntas_path}{$idPergunta}/";

            if (!is_dir($upload_path)) {
                $old = umask(0);
                mkdir("{$upload_perguntas_path}{$idPergunta}/", 0777);
                umask($old);
            }                        

            $config['upload_path'] = $upload_path;
            $config['allowed_types'] = 'xlsx|jpg|jpeg|png|txt|pdf';

            $this->load->library('upload');

            try {
                for($i=0;$i<$count;$i++){
                    if (!empty($_FILES['arquivos']['name'][$i])) {
                        $_FILES['arquivo']['name']     = $_FILES['arquivos']['name'][$i];
                        $_FILES['arquivo']['type']     = $_FILES['arquivos']['type'][$i];
                        $_FILES['arquivo']['tmp_name'] = $_FILES['arquivos']['tmp_name'][$i];
                        $_FILES['arquivo']['error']    = $_FILES['arquivos']['error'][$i];
                        $_FILES['arquivo']['size']     = $_FILES['arquivos']['size'][$i];
                        
                        $this->upload->initialize($config);
    
                        if (!$this->upload->do_upload('arquivo')) {
                            throw new Exception($this->upload->display_errors());
                        } else {
                            $file_data = $this->upload->data();
                            $file_ext = strtolower($file_data['file_ext']);
                            $full_path = $file_data['full_path'];
                            $upload_perguntas_path = config_item('upload_perguntas_path');    

                            $this->ctr_anexo_pergunta_model->save(array(
                                "hash"        => hash("md5", $file_data['raw_name']) . $file_ext,
                                "nome"        => $file_data['file_name'],
                                "criado_em"   => date("Y-m-d H:i:s"),
                                "id_pergunta" => $idPergunta
                            ));
                            // unlink($upload_path . $file_data['file_name']);
                        }
                    }
                }
            } catch (Exception $e) {
                $data['message'] = $e->getMessage();
                $data['error'] = true;

                $this->ctr_pergunta_model->delete(array($idPergunta));
                $this->ctr_grupo_pergunta_model->delete($idPergunta, $this->input->post("idGrupo"));

                $anexos = $this->ctr_anexo_pergunta_model->getEntriesByPergunta($idPergunta);
                $upload_perguntas_path = config_item('upload_perguntas_path')."{$idPergunta}/";

                foreach ($anexos as $anexo) {
                    unlink($upload_perguntas_path . $anexo->nome);
                }

                $this->ctr_anexo_pergunta_model->deleteByPergunta($idPergunta);
                
                rmdir($upload_perguntas_path);
            }
        }

        return response_json($data);
    }

    public function download($idPergunta)
    {
        $this->load->library("zip");

        $anexos = $this->ctr_anexo_pergunta_model->getEntriesByPergunta($idPergunta);

        $upload_perguntas_path = config_item('upload_perguntas_path')."{$idPergunta}/";

        foreach ($anexos as $anexo) {
            $filepath = $upload_perguntas_path . $anexo->nome;
            $this->zip->read_file($filepath);
        }

        $filename = date("Y-m-d_H:i:s") . "zip";

        $this->zip->download($filename);
    }

    public function relacionar()
    {
        $post = get_axios_post(); 

        if (!empty($post)) {
            $relation = $this->ctr_grupo_pergunta_model->save(array(
                'id_grupo'    => $post['idGrupo'],
                'id_pergunta' => $post['idPergunta']
            ));
        }

        if (!empty($relation)) {
            $message = "Sucesso! A pergunta foi registrada.";
        } else {
            $message = "Erro! Não foi possível registrar a pergunta.";
        }

        return response_json(array('message' => $message));
    }

    public function getPerguntas()
    {
        $idGrupo = $this->input->get('idGrupo');
        $pergunta = $this->input->get('pergunta') ? $this->input->get('pergunta') : "";

        $getByGroupRelation = $this->input->get('byGroupRelation') ? true : false;

        $data = array();

        if ($getByGroupRelation) {
            $data = $this->ctr_pergunta_model->getEntriesByGroupRelation($idGrupo);

            foreach ($data as $pergunta) {
                $pergunta->arquivos = $this->ctr_anexo_pergunta_model->getEntriesByPergunta($pergunta->id);
            }
        } else {
            $data = $this->ctr_pergunta_model->getEntries($pergunta);
        }

        return response_json(array("perguntas" => $data));
    }

    public function removerArquivo() 
    {
        $post = get_axios_post(); 

        $upload_perguntas_path = config_item('upload_perguntas_path')."{$post['idPergunta']}/";

        unlink($upload_perguntas_path . $post['arquivo']);

        $this->ctr_anexo_pergunta_model->deleteByPerguntaAndFileName($post['idPergunta'], $post['arquivo']);

        return response_json(array("error" => false));
    }
}