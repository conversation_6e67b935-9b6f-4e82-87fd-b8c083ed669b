<template>
    <div class="form-group">
        <div class="row">
            <div class="col-md-6" style="margin-bottom: 5px !important">
                <label for="nome" class="d-block">
                    {{index ? index + ' - ' : ''}}
                    {{label}} 
                     - ( 
                        <template v-for="(part, index) in partnumbers.split(',')">
                            <span :key="index">
                                {{ part.trim() }}
                                <i class="glyphicon glyphicon-info-sign text-primary" 
                                data-toggle="tooltip" 
                                data-html="true" 
                                style="cursor: help;" 
                                :title="getTooltipDescription(part.trim())">
                                </i>
                            </span>
                            <span v-if="index !== partnumbers.split(',').length - 1">, </span>
                        </template>
                      )
                    
                </label>
                <div class="dropdown" style="display: inline" v-if="item.arquivos.length > 0">
                    <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown"  aria-haspopup="true" aria-expanded="false">
                        <i class="glyphicon glyphicon-download"></i> 
                    </button>
                    <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                        <a class="dropdown-item btn-download" v-for="arquivo in item.arquivos" :title="arquivo.nome" :key="arquivo.id" :href="`${baseUrl}assets/perguntas/${item.id_pergunta}/${arquivo.nome}`" download>
                            {{ arquivo.nome }}
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8" style="padding-right: 10px">
                <div class="input-group">
                    <textarea :disabled="respondida" v-model="resposta" aria-label="..." @input="answering" class="size-textarea" rows="3" cols="90"> </textarea>

                </div>
            </div>      
            <div class="col-md-1" >
                <div class="input-group-btn">
                    <button :class="'btn ' + (respondida ? 'btn-success' : 'btn-default')" type="button" @click="skipAnswer" title="Marcar resposta como vazia" style="outline: none;">
                        <i class="glyphicon glyphicon-ok"></i>
                    </button>
                </div>
                <div v-if="this.permissionDelete.data == 1" class="input-group-btn">
                    <button :class="'btn ' + 'btn-danger'" type="button" @click="deleteQuestion(item.id,item.pergunta,item.ids)" style="outline: none">
                        <i class="glyphicon glyphicon-trash"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <input ref="file" id="input-file" style="width: 350px;" class="form-control input-file" name="inputFile" type="file" multiple="multiple" @change="previewFiles">
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12">
                <p><small><strong>({{ resposta.length }})</strong> quantidade de caracteres</small></p>
            </div>
        </div>
    </div>
</template>
<script>
import _ from 'lodash';

export default {
    data() {
        return {
            value: '',
            respondida: false
        }
    },
    props: {
        label: {
            required: true,
            type: String
        },
        partnumbers: {
            required: false,
            type: String
        },
        index: {
            required: false,
            type: String | Number
        },
        item: {
            required: true,
            type: Object | Array
        },
        baseUrl: {
            required: true,
            type: String
        },
        permissionDelete: {
            required: true,
            type: String
        },
        estabelecimento: {
            required: true,
            type: String
        },
        resposta: {
            required: true,
            type: String
        },
        tooltips: {
            required: false,
            type: Array
        }

    },
    methods: {
        getTooltipDescription(partnumber) {
            // Verifica se o partnumber está presente nos tooltips
            if (this.tooltips && this.tooltips[partnumber]) {
                return 'Descrição Curta:<br/>' + this.tooltips[partnumber].toUpperCase();
            } else {
                return 'Descrição não encontrada para o partnumber:<br/>' + partnumber;
            }
        },
        previewFiles(event) {
            let selectedFiles = event.target.files;

            if (!selectedFiles.length) {
                return this.$emit('removeFiles', this.item.ids);
            }

            let files = [];

            for (let i=0; i<selectedFiles.length; i++) {
                files.push(selectedFiles[i]);
            }

            this.$emit('storeFiles', {
                ids: this.item.ids,
                qtdFiles: files.length,
                fileNames: files.map(item => item.name),
                files
            });
        },
        deleteQuestion(id,pergunta_item,ids_perguntas) {
            let partnumbers = {
                id_pergunta: id,
                estabelecimento: this.estabelecimento,
                part_number: this.partnumbers,
                pergunta: pergunta_item,
                tipo_exclusao: 'Pergunta',
                ids: ids_perguntas
            };

            swal({
                title: "Atenção!",
                text: "Você deseja excluir a pergunta <br> "+pergunta_item,
                type: "warning",
                confirmButtonText: "OK",
                cancelButtonText: "Cancelar",
                showConfirmButton: true,
                showCancelButton: true,
                allowOutsideClick: false
            })
            .then((value) => {
                if (value)
                {
                  let result;
                  result =  this.$http.get('pr/perguntas/deletarPergunta', {
                        params: {
                            partnumbers
                        }
                    });
                window.location.reload();
                }

            });
        },
        skipAnswer() {
            this.respondida = !this.respondida;

            if (this.respondida == true)
            {
                this.item.pendente = 0;
            } else {
                if   (this.item.resposta)
                {
                    this.item.pendente = 0;
                } else {
                    this.item.pendente = 1;
                }
            }
        },
        answering() {
            if (this.resposta == '')
            {
                delete this.item.resposta;
            } else{
                this.item.resposta = this.resposta;
            }
            this.item.pendente = 0;
  
        }
    },
    mounted() {

        $('[data-toggle="tooltip"]').tooltip();

        $('[data-toggle="tooltip"]').on('shown.bs.tooltip', function () {
            $('.tooltip-inner').css('text-align', 'left');
        });

    },
}
</script>

<style scoped>

.text-primary {
    color: blue !important;
}

.form-group.files .form-control {
    padding: 5px 4px;
}

.btn-download {
    display: block;
    padding: 5px 12px;
    border-bottom: 1px solid rgb(211,211,211);
}

.btn-download:last-child {
    display: block;
    padding: 5px 12px;
    border-bottom: none;
}

.form-control .input-file {
    padding: 5px 5px !important;
}
.input-file{
    width: 200px;
}

.size-textarea{
    max-width: 750px !important;
    max-height: 80px !important;
}
</style>