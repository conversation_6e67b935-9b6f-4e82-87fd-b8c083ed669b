<template>
  <div id="cockpit">
    <ul class="nav nav-tabs">
      <li class="active">
        <a data-toggle="tab" href="#classificacao-fiscal">Classificação Fiscal</a>
      </li>
      <li>
        <a data-toggle="tab" href="#atributos">Atributos</a>
      </li>
      <li v-if="consultPermission">
        <a data-toggle="tab" href="#consultores">Consultores</a>
      </li>
    </ul>

    <div class="tab-content">
      <br />
      <div id="classificacao-fiscal" class="tab-pane fade in active">
        <div>
          <div class="row mb-20">
            <div class="col-md-4">
              <select class="form-control selectpicker" data-count-selected-text="Evento/Pacote ({0})"
                data-selected-text-format="count > 1" data-deselect-all-text="Nenhum" data-select-all-text="Todos"
                data-actions-box="true" data-live-search="true" name="evento[]" v-model="evento" id="evento"
                title="Evento/Pacote" multiple :disabled="eventoPacote.length > 0 ? false : true">
                <option value="sem_evento"><strong>Sem Evento/Pacote</strong></option>
                <option v-for="option in eventoPacote" :key="option.evento" :value="option.evento">
                  {{ option.evento }}
                </option>
              </select>
            </div>

            <div class="col-md-4">
              <select class="form-control selectpicker" data-count-selected-text="Estabelecimento ({0})"
                data-selected-text-format="count > 1" data-deselect-all-text="Nenhum" data-select-all-text="Todos"
                data-actions-box="true" data-live-search="true" name="evento[]" v-model="estabelecimento" id="evento"
                title="Estabelecimento" multiple :disabled="estabelecimentos.length > 0 ? false : true">
                <option v-for="item in estabelecimentos" :key="item.estabelecimento" :value="item.estabelecimento">
                  {{ item.estabelecimento }}
                </option>
              </select>
            </div>

            <button class="btn btn-primary btn-block search-button" type="button" style="width: 45px"
              @click="filterEventoPacote">
              <i class="glyphicon glyphicon-search"></i>
            </button>
            <div class="col-md-1"></div>
          </div>

          <div class="row mb-35">
            <CockpitCard graphic-key="StatusItens" card-title="Status Itens" route="getStatusItens" graphic-type="column"
              table-title="Status" ref="StatusItens" graphic-title="" col-size="12"
              :has-display-graphic="hasDisplayGraphic"></CockpitCard>
          </div>

          <hr />

          <div class="row mb-35 mt-35" v-if="dianaPermission">
            <CockpitCard card-title="Diana Utilização" graphic-key="DianaUtilizacao" route="getDianaUtilizacao"
              table-title="Diana Uso" ref="DianaUtilizacao" graphic-type="column" graphic-title="" col-size="6"
              timeout="100" :has-display-graphic="hasDisplayGraphic"></CockpitCard>

            <CockpitCard card-title="Diana Acuracidade" graphic-key="DianaAcuracidade" route="getDianaAcuracidade"
              ref="DianaAcuracidade" graphic-type="column" table-title="Diana" graphic-title="" col-size="6" timeout="200"
              :has-display-graphic="hasDisplayGraphic"></CockpitCard>
          </div>

          <div class="row mb-35 mt-35">
            <CockpitCard graphic-key="Atribuicao" card-title="Atribuição" table-title="Status GT" route="getAtribuicao"
              graphic-type="column" ref="Atribuicao" graphic-title="" col-size="6" timeout="300"
              :has-display-graphic="hasDisplayGraphic"></CockpitCard>
            <CockpitCard card-title="Status NCM" graphic-key="NcmDivergente" route="getNcmDivergentes"
              table-title="Status NCM" graphic-type="column" ref="NcmDivergente" graphic-title="" col-size="6"
              timeout="400" :has-display-graphic="hasDisplayGraphic"></CockpitCard>
          </div>
        </div>
      </div>
      <div id="atributos" class="tab-pane fade">
        <div>
          <div class="row mb-20">
            <div class="col-md-4">
              <select class="form-control selectpicker" data-count-selected-text="Evento/Pacote ({0})"
                data-selected-text-format="count > 1" data-deselect-all-text="Nenhum" data-select-all-text="Todos"
                data-actions-box="true" data-live-search="true" name="evento[]" v-model="eventoAttrs" id="evento"
                title="Evento/Pacote" multiple :disabled="eventoPacote.length > 0 ? false : true">
                <option value="sem_evento"><strong>Sem Evento/Pacote</strong></option>
                <option v-for="option in eventoPacote" :key="option.evento" :value="option.evento">
                  {{ option.evento }}
                </option>
              </select>
            </div>
            <div class="col-md-4">
              <select class="form-control selectpicker" data-count-selected-text="Status de Atributos ({0})"
                data-selected-text-format="count > 1" data-deselect-all-text="Nenhum" data-select-all-text="Todos"
                data-actions-box="true" data-live-search="true" name="status_attr[]" v-model="status_attr"
                id="status_attr" title="Status de Atributos" multiple>
                <option v-for="status in status_attrs" :key="status.slug" :value="status.id">
                  {{ status.status }}
                </option>
              </select>
            </div>
            <div class="col-md-4">
              <select class="form-control selectpicker" data-count-selected-text="Status de Classificação ({0})"
                data-selected-text-format="count > 1" data-deselect-all-text="Nenhum" data-select-all-text="Todos"
                data-actions-box="true" data-live-search="true" name="status_classificacao[]"
                v-model="status_classificacao" id="status_classificacao" title="Status de Classificação" multiple>
                <option v-for="status in status_classificacaos" :key="status.status" :value="status.id">
                  {{ status.status_formatado }}
                </option>
              </select>
            </div>
          </div>
          <div class="row mb-20">
            <div class="col-md-4">
              <select class="form-control selectpicker" data-count-selected-text="Status de Preenchimento ({0})"
                data-selected-text-format="count > 1" data-deselect-all-text="Nenhum" data-select-all-text="Todos"
                data-actions-box="true" data-live-search="true" name="status_preenchimento[]"
                v-model="status_preenchimento" id="status_preenchimento" title="Status de Preenchimento" multiple>
                <option v-for="status in status_preenchimentos" :key="status.id" :value="status.id">
                  {{ status.descricao }}
                </option>
              </select>
            </div>
            <div class="col-md-4">
              <select class="form-control selectpicker" data-count-selected-text="Status de Prioridade ({0})"
                data-selected-text-format="count > 1" data-deselect-all-text="Nenhum" data-select-all-text="Todos"
                data-actions-box="true" data-live-search="true" name="prioridade[]" v-model="prioridade" id="prioridade"
                title="Status de Prioridade" multiple>
                <option v-for="prioridade in prioridades" :key="prioridade.id_prioridade"
                  :value="prioridade.id_prioridade">
                  {{ prioridade.nome }}
                </option>
              </select>
            </div>
            <div class="col-md-4">
            <select  class="form-control selectpicker"
              data-count-selected-text="NCM Proposto ({0})"
              data-selected-text-format="count > 1"
              data-deselect-all-text="Nenhum"
              data-select-all-text="Todos"
              data-actions-box="true"
              data-live-search="true"
              name="ncm_proposto[]"
              v-model="ncm_proposto"
              id="ncm_proposto"
              title="NCM Proposto"
              multiple>
              <option v-for="ncm_proposto in ncm_propostos" :key="ncm_proposto.ncm_proposto"
                :value="ncm_proposto.ncm_proposto">
                {{ ncm_proposto.ncm_proposto }}
              </option>
              <option v-if="!isLoadingNcm" >
                Carregando dados...
              </option>
            </select>
          </div>
 
          </div>
          <div class="row mb-20">
            <div class="col-md-12">
              <textarea name="part_numbers" type="text" class="form-control" placeholder="Código do produto"
                style="margin: 10px 0; height: 50px; width: 100%" v-model="search">
                </textarea>
            </div>
          </div>
          <div class="row mb-20">
            <div class="col-md-12">

              <div class="panel-group" id="accordionx" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                  <div class="panel-heading" role="tab" id="headingOne">
                    <h4 class="panel-title">
                      <a role="button" data-toggle="collapse" data-parent="#accordionx" href="#collapseOnex"
                        aria-expanded="false" aria-controls="collapseOnex">
                        <span class="glyphicon glyphicon-filter" aria-hidden="true"></span> Filtros Avançados
                      </a>
                    </h4>
                  </div>
                  <div id="collapseOnex" class="panel-collapse collapse" role="tabpanel" aria-labelledby="headingOne"
                    aria-expanded="false">
                    <div class="panel-body">
                      <div class="row mb-20">

                        <div class="col-md-4">
                          <select class="form-control selectpicker" data-count-selected-text="Status de Integração ({0})"
                            data-selected-text-format="count > 1" data-deselect-all-text="Nenhum"
                            data-select-all-text="Todos" data-actions-box="true" data-live-search="true"
                            name="status_integracao[]" v-model="status_integracao" id="status_integracao"
                            title="Status de Integração" multiple>
                            <option v-for="status_integracao in status_integracaos" :key="status_integracao.slug"
                              :value="status_integracao.id">
                              {{ status_integracao.status }}
                            </option>
                          </select>
                        </div>
                        <div class="col-md-4">

                          <select class="form-control selectpicker" data-count-selected-text="Owner ({0})"
                            data-selected-text-format="count > 1" data-deselect-all-text="Nenhum"
                            data-select-all-text="Todos" data-actions-box="true" data-live-search="true" name="owner[]"
                            v-model="owner" id="owner" title="Owner" multiple>
                            <option v-for="owner in owners" :key="owner.codigo" :value="owner.codigo">
                              {{ owner.descricao }}
                            </option>
                          </select>
                        </div>

                        <div class="col-md-4">
                          <select class="form-control selectpicker" data-count-selected-text="Estabelecimento ({0})"
                            data-selected-text-format="count > 1" data-deselect-all-text="Nenhum"
                            data-select-all-text="Todos" data-actions-box="true" data-live-search="true" name="evento[]"
                            v-model="estabelecimentoAttrs" id="evento" title="Estabelecimento" multiple
                            :disabled="estabelecimentos.length > 0 ? false : true">
                            <option v-for="item in estabelecimentos" :key="item.estabelecimento"
                              :value="item.estabelecimento">
                              {{ item.estabelecimento }}
                            </option>
                          </select>
                        </div>
                      </div>
                      <div class="row mb-20">
                        <div class="col-md-4">
                          <select class="form-control selectpicker" data-count-selected-text="Responsável ({0})"
                            data-selected-text-format="count > 1" data-deselect-all-text="Nenhum"
                            data-select-all-text="Todos" data-actions-box="true" data-live-search="true"
                            name="responsavel[]" v-model="responsavel" id="responsavel" title="Responsável" multiple
                            :disabled="responsaveis.length > 0 ? false : true">
                            <option v-for="item in responsaveis" :key="item.id_resp_engenharia"
                              :value="item.id_resp_engenharia">
                              {{ item.nome }}
                            </option>
                          </select>
                        </div>


                        <div class="col-md-4">

                          <select class="form-control selectpicker" data-count-selected-text="Objetivos ({0})"
                            data-selected-text-format="count > 1" data-deselect-all-text="Nenhum"
                            data-select-all-text="Todos" data-actions-box="true" data-live-search="true" name="objetivo[]"
                            v-model="objetivo" id="objetivo" title="Objetivos" multiple>
                            <option v-for="objetivo in objetivos" :key="objetivo" :value="objetivo">
                              {{ objetivo }}
                            </option>
                          </select>


                        </div>
                      </div>

                    </div>

                  </div>
                </div>
              </div>

              <input type="hidden" name="filtered" value="1">
              <button @click="searchAttrs" class="btn btn-primary btn-block" id="btn-search">Pesquisar</button>
              <div class="col-sm-12 text-right">
                <div class="form-group">
                  <button type="button" @click="limparFiltros" class="btn btn-link pull-right">Limpar Pesquisa</button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="row mb-35">
          <AtributosCard graphic-key="StatusAtributos" card-title="Status de preenchimento de atributos"
            route="getStatusAtributos" graphic-type="column" table-title="Status" ref="StatusAtributos" graphic-title=""
            col-size="12" :has-display-graphic="hasDisplayGraphic" :hasConsult="false"></AtributosCard>
        </div>

      </div>

      <div id="consultores" class="tab-pane fade">
        <div>
          <div class="row mb-20">
            <div class="col-md-4">
              <select class="form-control selectpicker"  data-deselect-all-text="Nenhum" data-select-all-text="Todos"
                data-actions-box="true" data-live-search="true" name="status_ano[]" v-model="status_ano" id="status_ano"
                title="Ano"  >
                <option v-for="option in status_anos" :key="option" :value="option">
                  {{ option }}
                </option>
              </select>
            </div>

            <div class="col-md-4">
              <select class="form-control selectpicker"  data-deselect-all-text="Nenhum" data-select-all-text="Todos"
                data-actions-box="true" data-live-search="true" name="status_mes[]" v-model="status_mes" id="status_mes"
                title="Mes"  >
                <option v-for="option in status_mess" :key="option" :value="option">
                  {{ option }}
                </option>
              </select>
            </div>
            <div class="col-md-4">
              <select class="form-control selectpicker"  data-deselect-all-text="Nenhum" data-select-all-text="Todos"
                data-actions-box="true" data-live-search="true" name="status_empresa[]" v-model="status_empresa" id="status_empresa"
                title="Empresa" multiple data-count-selected-text="Empresa ({0})"
                data-selected-text-format="count > 1"  >
                <option v-for="option in status_empresas" :key="option.id_empresa" :value="option.id_empresa">
                  {{ option.nome_fantasia }}
                </option>
                <option v-if="!isLoadingEmp" >
                Carregando dados...
              </option>
              </select>
            </div>

          </div>
          <div class="row mb-20">
            <div class="col-md-4">
              <select class="form-control selectpicker"  data-deselect-all-text="Nenhum" data-select-all-text="Todos"
                data-actions-box="true" data-live-search="true" name="status_consultor[]" v-model="status_consultor" id="status_consultor"
                title="Consultor" multiple data-count-selected-text="Consultor ({0})"
                data-selected-text-format="count > 1"  >
                <option v-for="option in status_consultors" :key="option.id_usuario" :value="option.id_usuario">
                  {{ option.nome }}
                </option>
                <option v-if="!isLoadingConsult" >
                Carregando dados...
              </option>
              </select>
            </div>

          </div>
 
          <div class="row mb-20">
            <div class="col-md-12">

 

              <input type="hidden" name="filtered" value="1">
              <button @click="searchConsult" class="btn btn-primary btn-block" id="btn-search-consult">Pesquisar</button>
              <div class="col-sm-12 text-right">
                <div class="form-group">
                  <button type="button" @click="limparFiltrosConsult" class="btn btn-link pull-right">Limpar Pesquisa</button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="row mb-35">
          <AtributosCard graphic-key="StatusConsult" card-title="Classificação mensal dos consultores"
            route="getStatusConsult" graphic-type="column" table-title="Status" ref="StatusConsult" graphic-title=""
            col-size="12" :has-display-graphic="hasDisplayGraphic" :hasConsult="true"></AtributosCard>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
import CockpitCard from "./CockpitCard";
import AtributosCard from "./AtributosCard";

import _ from "lodash";

export default {
  data() {
    return {
      isLoadingConsult: false,
      isLoadingEmp: false,
      isLoadingNcm: false,
      isLoading: false,
      fullPage: true,
      updateComponent: 0,
      boolHasDisplayGraphic: false,
      status_mes:  [],
      status_ano:  [],
      status_consultor:  [],
      status_empresa:  [],
      status_mess:  [],
      status_anos:  [],
      status_consultors:  [],
      status_empresas:  [],
      eventoPacote: [],
      evento: [],
      estabelecimentos: [],
      eventoAttrs: [],
      estabelecimentoAttrs: [],
      estabelecimento: [],
      responsaveis: [],
      responsavel: [],
      status_attr: [],
      status_attrs: [],
      status_classificacao: [],
      status_classificacaos: [],
      status_preenchimento: [],
      status_preenchimentos: [],
      prioridade: [],
      prioridades: [],
      ncm_proposto: [],
      ncm_propostos: [],
      status_integracao: [],
      status_integracaos: [],
      owners: [],
      owner: [],
      objetivos: [],
      objetivo: [],
      search: "",
      statusAttrsLoaded: false,
      statusClassLoaded: false,
      statusPreLoaded: false,
      statusPrioridadeLoaded: false,
      statusObjetivoLoaded: false,
      statusAnoLoaded: false,
      statusMesLoaded: false,
      statusConsultLoaded: false,
      statusEmpresaLoaded: false,
      statusNcmLoaded: false,
      statusIntegracaoLoaded: false,
      statusOwnerLoaded: false,

    };
  },
  props: {
    consultPermission: {
      type: Boolean,
      required: true,
    },
    dianaPermission: {
      type: Boolean,
      required: true,
    },
    hasDisplayGraphic: {
      type: Boolean,
      required: true,
    },
  },
  methods: {

    async updateComponentsConsult(status_ano, status_mes, status_consultor, status_empresa) {
      if (status_consultor && status_consultor.length > 150) {
        swal ({
          title: "Atenção",
          text: "Selecione no máximo 150 consultores.",
          icon: "warning",
          button: "OK",
        });
        return;
      }  

      if (status_mes == '' || status_ano == '' || status_mes == null || status_ano == null) {
        swal ({
          title: "Atenção",
          text: "Por favor, selecione o ano e o mês de referência.",
          icon: "warning",
          button: "OK",
        });
        return;
      } 
      
      document.getElementById("btn-search-consult").disabled = true;
      await this.$refs.StatusConsult.updateMyselfConsult(status_ano, status_mes, status_consultor, status_empresa);
    },
    async updateComponentsAttrs(eventoAttrs, estabelecimentoAttrs, responsavel, search, status_attr, status_classificacao, status_preenchimento, prioridade, ncm_proposto, status_integracao, owner, objetivo) {
      document.getElementById("btn-search").disabled = true;
      await this.$refs.StatusAtributos.updateMyselfAttr(eventoAttrs, estabelecimentoAttrs, responsavel, search, status_attr, status_classificacao, status_preenchimento, prioridade, ncm_proposto, status_integracao, owner, objetivo);
    },
    async updateComponents(evento, estabelecimento) {
      document.getElementById("btn-search").disabled = true;
      await this.$refs.StatusItens.updateMyself(evento, estabelecimento);
      if (this.dianaPermission) {
        await this.$refs.DianaUtilizacao.updateMyself(evento, estabelecimento);
        await this.$refs.DianaAcuracidade.updateMyself(evento, estabelecimento);
      }
      await this.$refs.Atribuicao.updateMyself(evento, estabelecimento);
      await this.$refs.NcmDivergente.updateMyself(evento, estabelecimento);

    },
    filterEventoPacote() {
      this.updateComponents(this.evento, this.estabelecimento);
    },
    searchAttrs() {
      this.updateComponentsAttrs(this.eventoAttrs, this.estabelecimentoAttrs, this.responsavel, this.search, this.status_attr, this.status_classificacao, this.status_preenchimento, this.prioridade, this.ncm_proposto, this.status_integracao, this.owner, this.objetivo);
    },
    searchConsult() {
      this.updateComponentsConsult(this.status_mes, this.status_ano, this.status_consultor, this.status_empresa);
    },
 
    getEventoPacote() {
      this.$http.get(`cockpit/getEventoPacote`).then(({ data }) => {
        this.eventoPacote = data.data;
      });
    },
    getEstabelecimentos() {
      this.$http.get(`cockpit/getEstabelecimentos`).then(({ data }) => {
        this.estabelecimentos = data.data;
      });
    },
    getResponsaveis() {

      this.$http.get(`cockpit/getResponsaveis`).then(({ data }) => {
        this.responsaveis = data.data;
        // this.responsavel = this.responsaveis.map(item => item.id_resp_engenharia);
        document.getElementById("btn-search").disabled = false;
      });

    },
    getStatusAttrs() {
      if (!this.statusAttrsLoaded) {
        this.$http.get('cockpit/getStatusAttrs')
          .then(({ data }) => {
            console.log(data);
            this.status_attrs = data.data;
            this.statusAttrsLoaded = true;
            this.$nextTick(() => {
              $(".selectpicker").selectpicker("refresh");
            });
          })
          .catch(error => {
            console.error("Erro ao buscar status de atributos:", error);
          });
      }
    },
    getStatusClass() {
      if (!this.statusClassLoaded) {
        this.$http.get('cockpit/getStatusClass')
          .then(({ data }) => {
            console.log(data);
            this.status_classificacaos = data.data;
            this.statusClassLoaded = true;
            this.$nextTick(() => {
              $(".selectpicker").selectpicker("refresh");
            });
          })
          .catch(error => {
            console.error("Erro ao buscar status de atributos:", error);
          });
      }
    },
    getStatusPre() {
      if (!this.statusPreLoaded) {
        this.$http.get('cockpit/getStatusPreenchimento')
          .then(({ data }) => {
            console.log(data);
            this.status_preenchimentos = data.data;
            this.statusPreLoaded = true;
            this.$nextTick(() => {
              $(".selectpicker").selectpicker("refresh");
            });
          })
          .catch(error => {
            console.error("Erro ao buscar status de atributos:", error);
          });
      }
    },
    getPrioridade() {
      if (!this.statusPrioridadeLoaded) {
        this.$http.get('cockpit/getPrioridade')
          .then(({ data }) => {
            console.log(data);
            this.prioridades = data.data;
            this.statusPrioridadeLoaded = true;
            this.$nextTick(() => {
              $(".selectpicker").selectpicker("refresh");
            });
          })
          .catch(error => {
            console.error("Erro ao buscar status de atributos:", error);
          });
      }
    },
    getStatusAno() {
      if (!this.statusAnoLoaded) {
        this.$http.get('cockpit/getStatusAnos')
          .then(({ data }) => {
            console.log(data);
            this.status_anos = data.data;
            this.statusAnoLoaded = true;
            this.$nextTick(() => {
              $(".selectpicker").selectpicker("refresh");
            });
          })
          .catch(error => {
            console.error("Erro ao buscar status de atributos:", error);
          });
      }
    },
    getStatusMes() {
      if (!this.statusMesLoaded) {
        this.$http.get('cockpit/getStatusMes')
          .then(({ data }) => {
            console.log(data);
            this.status_mess = data.data;
            this.statusMesLoaded = true;
            this.$nextTick(() => {
              $(".selectpicker").selectpicker("refresh");
            });
          })
          .catch(error => {
            console.error("Erro ao buscar status de atributos:", error);
          });
      }
    },
    getStatusConsultor() {
      if (!this.statusConsultLoaded) {
        this.$http.get('cockpit/getStatusConsultor')
          .then(({ data }) => {
            this.isLoadingConsult = true;
 
            this.status_consultors = data.data;
            this.statusConsultLoaded = true;
            this.$nextTick(() => {
              $(".selectpicker").selectpicker("refresh");
            });
          })
          .catch(error => {
            console.error("Erro ao buscar status de atributos:", error);
          });
      }
    },
    getStatusEmpresa() {
      if (!this.statusEmpresaLoaded) {
        this.$http.get('cockpit/getStatusEmpresa')
          .then(({ data }) => {
            this.isLoadingEmp = true;
 
            this.status_empresas = data.data;
            this.statusEmpresaLoaded = true;
            this.$nextTick(() => {
              $(".selectpicker").selectpicker("refresh");
            });
          })
          .catch(error => {
            console.error("Erro ao buscar status de atributos:", error);
          });
      }
    },
    
    getObjetivo() {
      if (!this.statusObjetivoLoaded) {
        this.$http.get('cockpit/getObjetivos')
          .then(({ data }) => {
            console.log(data);
            this.objetivos = data.data;
            this.statusObjetivoLoaded = true;
            this.$nextTick(() => {
              $(".selectpicker").selectpicker("refresh");
            });
          })
          .catch(error => {
            console.error("Erro ao buscar status de atributos:", error);
          });
      }
    },
    getNcm() {
      if (!this.statusNcmLoaded) {
        this.$http.get('cockpit/getNcm')
          .then(({ data }) => {
            console.log(data);
            this.ncm_propostos = data.data;
            this.statusNcmLoaded = true;
            this.isLoadingNcm = true;
            this.$nextTick(() => {
              $(".selectpicker").selectpicker("refresh");
            });
          })
          .catch(error => {
            console.error("Erro ao buscar status de atributos:", error);
          });
      }
    },
    getStatusIntegracao() {
      if (!this.statusIntegracaoLoaded) {
        this.$http.get('cockpit/getStatusIntegracao')
          .then(({ data }) => {
            console.log(data);
            this.status_integracaos = data.data;
            this.statusIntegracaoLoaded = true;
            this.$nextTick(() => {
              $(".selectpicker").selectpicker("refresh");
            });
          })
          .catch(error => {
            console.error("Erro ao buscar status de atributos:", error);
          });
      }
    },
    getOwner() {
      if (!this.statusOwnerLoaded) {
        this.$http.get('cockpit/getOwner')
          .then(({ data }) => {
            console.log(data);
            this.owners = data.data;
            this.statusOwnerLoaded = true;
            this.$nextTick(() => {
              $(".selectpicker").selectpicker("refresh");
            });
          })
          .catch(error => {
            console.error("Erro ao buscar status de atributos:", error);
          });
      }
    },
    limparFiltrosConsult() {
      let nomesDosMeses = [
      "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho",
      "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"
    ];

    let currentDate = new Date();
    let currentMonthIndex = currentDate.getMonth();  

    this.status_ano = currentDate.getFullYear();
    this.status_mes = nomesDosMeses[currentMonthIndex]; 
 
    this.status_consultor = [];
    this.status_empresa = [];

    this.$nextTick(() => {
      $('#status_ano, #status_mes, #status_empresa, #status_consultor').selectpicker('refresh');
    });

      this.updateComponentsConsult(this.status_mes, this.status_ano,  this.status_consultor, this.status_empresa);

    },
    limparFiltros() {
      this.evento = [];
      this.estabelecimento = [];
      this.eventoAttrs = [];
      this.estabelecimentoAttrs = [];
      this.responsavel = [];
      this.status_attr = [];
      this.status_classificacao = [];
      this.status_preenchimento = [];
      this.prioridade = [];
      this.ncm_proposto = [];
      this.status_integracao = [];
      this.owner = [];
      this.objetivo = [];
      this.search = "";
      // Atualiza os componentes do CockpitCard e AtributosCard
      this.updateComponents(this.evento, this.estabelecimento);
      this.updateComponentsAttrs(this.eventoAttrs, this.estabelecimentoAttrs, this.responsavel, this.search, this.status_attr, this.status_classificacao, this.status_preenchimento, this.prioridade, this.ncm_proposto, this.status_integracao, this.owner, this.objetivo);
      // Refresh selectpicker
      this.$nextTick(() => {
        $(".selectpicker").selectpicker("refresh");
      });
    },

  },
  updated() {
    this.$nextTick(function () {
      $(".selectpicker").selectpicker("refresh");
    });
  },
  mounted() {
    document.getElementById("btn-search").disabled = true;
    this.getStatusAno();
    this.getStatusMes();

    let nomesDosMeses = [
      "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho",
      "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"
    ];

    let currentDate = new Date();
    let currentMonthIndex = currentDate.getMonth();  

    this.status_ano = currentDate.getFullYear();
    this.status_mes = nomesDosMeses[currentMonthIndex];  
    this.$nextTick(() => {
      $('#status_ano, #status_mes').selectpicker('refresh');
    });

    this.$nextTick(() => {
      $('#status_ano, #status_mes').selectpicker('refresh');
    });
 

    let self = this;
    setTimeout(() => self.getEventoPacote(), 5000);
    setTimeout(() => self.getEstabelecimentos(), 5000);
    setTimeout(() => self.getResponsaveis(), 5000);
    $('#status_attr').on('show.bs.select', () => {
      this.getStatusAttrs();
    });
    $('#status_classificacao').on('show.bs.select', () => {
      this.getStatusClass();
    });
    $('#status_preenchimento').on('show.bs.select', () => {
      this.getStatusPre();
    });
    $('#prioridade').on('show.bs.select', () => {
      this.getPrioridade();
    });
    $('#ncm_proposto').on('show.bs.select', () => {
      this.getNcm();
    });
    $('#status_integracao').on('show.bs.select', () => {
      this.getStatusIntegracao();
    });
    $('#owner').on('show.bs.select', () => {
      this.getOwner();
    });
    $('#objetivo').on('show.bs.select', () => {
      this.getObjetivo();
    });

    $('#status_ano').on('show.bs.select', () => {
      this.getStatusAno();
    });

    $('#status_mes').on('show.bs.select', () => {
      this.getStatusMes();
    });
    $('#status_consultor').on('show.bs.select', () => {
      this.getStatusConsultor();
    });
    $('#status_empresa').on('show.bs.select', () => {
      this.getStatusEmpresa();
    });
    

    console.log(self.hasDisplayGraphic);
  },
  components: {
    CockpitCard,
    AtributosCard
  },
};
</script>