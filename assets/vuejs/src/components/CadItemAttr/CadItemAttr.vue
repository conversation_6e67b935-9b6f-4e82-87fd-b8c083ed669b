<style scoped>
.red-text {
    color: red;
}

.alert-anim {
    animation: alert-anim 10s ease forwards;
}

@keyframes alert-anim {
    0% {
        scale: .9;
        opacity: 0%;

    }

    5%,
    95% {
        scale: 1;
        opacity: 100%;

    }

    100% {
        scale: .9;
        opacity: 0%;
    }
}

.check {
    cursor: pointer;
    transition: .5s;
}

.check-success {
    color: #3DB23D;
}

.check-warn {
    color: #ddd;
}

.border {
    transition: .5s;
}

.border-success {
    border-right: .5rem solid #3DB23D;
}

.border-warn {
    border-right: .5rem solid #ddd;
}

.justify-content-between {
    justify-content: space-between;
}

.align-items-middle {
    justify-content: center;
    align-items: center;
}

.template {
    margin-top: 1.5rem;
    border-left: 1px solid lightgrey;
}

.panel {
    transition: .25s;
}

.panel:hover {
    box-shadow: 0 15px 30px rgb(0 0 0 / 15%);
}

.panel-custom {
    background: unset !important;
    margin-bottom: 0 !important;
    box-shadow: unset !important;
    -webkit-box-shadow: unset !important;
}

.d-flex {
    display: flex;
}

.pt-0 {
    padding-top: 0 !important;
}

.pe-0 {
    padding-right: 0 !important;
}

.ps-5 {
    padding-left: 2rem;
}

.px-4 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

.pt-5 {
    padding-top: 2rem;
}

.pb-4 {
    padding-bottom: 1.5rem;
}
</style>

<script>
import moment from 'moment';
import _ from "lodash";
import axios from "axios";

import CadItemAttrInput from './CadItemAttrInput.vue';

export default {
    name: 'CadItemAttr',
    data() {
        return {
            successMessage: false,
            dangerMessage: false,
            dangerText: '',
            attr: null,
            // currentValue: null, 
            key: 0,
            changed: false,
            isDianaLoading: false,
            alerts:false,
            descricao_item: '',
            attrs_processados_diana: [],
        }
    },
    props: {
        params: {
            required: true,
            type: Object
        },
        addClass: {
            required: false,
            type: String
        },
        initialAttr: {
            required: true,
            type: Object
        },
        itemDescription: {
            required: false,
            type: String,
            default: ''
        },
        registro_item: {
            required: false,
            type: String
        },
        parentAttr: {},
        isEditable: {},
        integra_diana_company: false,
        email_usuario: '',
    },
    methods: {
        showInfo() {

            const colors = {
                Produto: '#FF0000',
                LPCO: '#0000FF',
                Duimp: '#FF00FF',
                'Tratamento administrativo': '#00FF00',
                'Cadastro de intervenientes': '#FFFFFF',
                'Tratamento tributário': '#FFA500',
            };

            var result =
                '<div style="text-align:left; padding: 5px 10px; font-size: 12px">';

            if (this.attr.codigo) {
                result += `<div style="margin-bottom:5px;"><strong> Codigo: </strong> <span style="font-weight:300"> ${this.attr.codigo}</span></div>`;
            }
            if (this.attr.modalidade) {
                result += `<div style="margin-bottom:5px;"><strong> Modalidade: </strong> <span style="font-weight:300"> ${this.attr.modalidade}</span> </div>`;
            }
            if (this.attr.dataInicioVigencia) {
                result += `<div style="margin-bottom:5px;"><strong> Vigência: </strong> <span style="font-weight:300"> ${this.formatDate(
                    this.attr.dataInicioVigencia
                )} ${this.attr.dataFimVigencia
                        ? 'até ' + this.formatDate(this.attr.dataFimVigencia)
                        : 'até indefinido'
                    }</span> </div>`;
            }
            if (this.attr.orgaos) {
                result += `<div style="margin-bottom:5px;"><strong> Orgãos: </strong> <span style="font-weight:300"> ${this.attr.orgaos.toString()}</span> </div>`;
            }
            if (this.attr.objetivos) {
                result += `<strong>Objetivos:</strong><br>`;
                this.attr.objetivos.forEach((objetivo) => {
                    result += `<span style="color:${colors[objetivo.descricao]}">- ${objetivo.descricao
                        }</span><br>`;
                });
            }
            if (this.attr.orientacaoPreenchimento) {
                result += `<div style="margin-top:5px;"><strong> Orientacao de preenchimento: </strong> <span style="font-weight:300"> ${this.attr.orientacaoPreenchimento} </span></div> <br>`;
            }
            if (this.attr.tamanhoMaximo) {
                result += `<strong> Preenchimento máximo: </strong> ${this.attr.tamanhoMaximo} <br>`;
            }
            if (this.attr.casasDecimais) {
                result += `<strong> Casas decimais: </strong> ${this.attr.casasDecimais} <br>`;
            }
            result += '</div>';

            return result;

            var result = '<p>';

            if (this.attr.modalidade) {
                result += `<strong> Modalidade: </strong> ${this.attr.modalidade} <br>`;
            }

            if (this.attr.dataInicioVigencia) {
                result += `<strong> Data inicio vigência: </strong> ${this.formatDate(this.attr.dataInicioVigencia)} ${this.attr.dataFimVigencia ? ('até ' + this.formatDate(this.attr.dataFimVigencia)) : ''} <br>`;
            }

            if (this.attr.orgaos) {
                result += `<strong> Orgãos: </strong> ${this.attr.orgaos.toString()} <br>`;
            }

            if (this.attr.objetivos) {
                result += `<strong> Objetivos: </strong> ${this.attr.objetivos.map(objetivo => objetivo.descricao).toString()}`;
            }

            result += '</p>';

            return result;
        },
        formatDate(date) {
            return moment(date).format('DD/MM/YYYY');
        },
        filledValue({ key }) {
            if (!Array.isArray(this.filledValues)) {
                this.filledValues = [];
            }

            if (this.filledValues.indexOf(key) === -1) {
                this.filledValues.push(key);
            }
        },

        fillValue(initialValue, currentValue) {
            this.attr.dbdata.codigo = currentValue;

            if (initialValue != currentValue) {
                this.currentValue = currentValue;

                this.$emit('onFilledValue', { key: this.attr.codigo });
            }
        },
        handleChange({ initialValue, currentValue }) {
            this.fillValue(initialValue, currentValue);
        },
        handleSelect({ initialValue, currentValue }) {
            this.handleAttributeConditioning(currentValue);
            var value = currentValue;

            if (this.attr.formaPreenchimento.toUpperCase() === "BOOLEANO" && (value > 0 || value == "Sim" || value == "SIM" || value == "sim")) {
                value = "true";
            }

            if (this.attr.atributoCondicionante) {
                this.attr.condicionados.forEach((item) => {
                    const el = $(`#${item.atributo.codigo}`);
                    let composicao     = null;
                    let condicao_two   = '';
                    let condicao_three = '';
                    let condicao_four  = '';
                    let condicao_five  = '';
                    let condicao_six   = '';
                    let condicao_seven = '';
                    let condicao_eight = '';
                    let condicao_nine  = '';
                    let condicao_ten   = '';
                    
                    let composicao_one   = '';
                    let composicao_two   = '';
                    let composicao_three = '';
                    let composicao_four  = '';
                    let composicao_five  = '';
                    let composicao_six   = '';
                    let composicao_seven = '';
                    let composicao_eight = '';
                    let composicao_nine  = '';
                    let composicao_ten   = '';

                    if (!_.isEmpty(value)) {
                        if (value instanceof Array) {
                            const values = value;
                            values.forEach((value) => {
                                const condicao = eval(`"${value ? value : 0}" ${item.condicao.operador} "${item.condicao.valor}"  `);

                                condicao ? el.stop().slideDown("fast") : el.stop().slideUp("fast");
                            });

                        } else {
                            if (item.condicao.hasOwnProperty('composicao')) {
                                if ((item.condicao.condicao.valor == false || item.condicao.condicao.valor == "false") && value == 0) {
                                    value = false;
                                }
                                condicao_two = eval(`"${value ? value : 0}" ${item.condicao.condicao.operador} "${item.condicao.condicao.valor}" `);
                                composicao_two = item.condicao.condicao.composicao ? item.condicao.condicao.composicao : '';
                                
                                if (item.condicao.condicao.hasOwnProperty('condicao')) {

                                    if ((item.condicao.condicao.condicao.valor == false || item.condicao.condicao.condicao.valor == "false") && value == 0) {
                                        value = false;
                                    }
                                    condicao_three = eval(`"${value ? value : 0}" ${item.condicao.condicao.condicao.operador} "${item.condicao.condicao.condicao.valor}" `);
                                    composicao_three = item.condicao.condicao.condicao.composicao ? item.condicao.condicao.condicao.composicao : '';

                                    if (item.condicao.condicao.condicao.hasOwnProperty('condicao')) {
                                        if ((item.condicao.condicao.condicao.condicao.valor == false || item.condicao.condicao.condicao.condicao.valor == "false") && value == 0) {
                                            value = false;
                                        }
                                        condicao_four = eval(`"${value ? value : 0}" ${item.condicao.condicao.condicao.condicao.operador} "${item.condicao.condicao.condicao.condicao.valor}" `);
                                        composicao_four = item.condicao.condicao.condicao.condicao.composicao ? item.condicao.condicao.condicao.condicao.composicao : '';
                                        if (item.condicao.condicao.condicao.condicao.hasOwnProperty('condicao')) {
                                            if ((item.condicao.condicao.condicao.condicao.condicao.valor == false || item.condicao.condicao.condicao.condicao.condicao.valor == "false") && value == 0) {
                                                value = false;
                                            }
                                            condicao_five = eval(`"${value ? value : 0}" ${item.condicao.condicao.condicao.condicao.condicao.operador} "${item.condicao.condicao.condicao.condicao.condicao.valor}" `);
                                            composicao_five = item.condicao.condicao.condicao.condicao.condicao.composicao ? item.condicao.condicao.condicao.condicao.condicao.composicao : '';
                                            if (item.condicao.condicao.condicao.condicao.condicao.hasOwnProperty('condicao')) {
                                                if ((item.condicao.condicao.condicao.condicao.condicao.condicao.valor == false || item.condicao.condicao.condicao.condicao.condicao.condicao.valor == "false") && value == 0) {
                                                    value = false;
                                                }
                                                condicao_six = eval(`"${value ? value : 0}" ${item.condicao.condicao.condicao.condicao.condicao.condicao.operador} "${item.condicao.condicao.condicao.condicao.condicao.condicao.valor}" `);
                                                composicao_six = item.condicao.condicao.condicao.condicao.condicao.condicao.composicao ? item.condicao.condicao.condicao.condicao.condicao.condicao.composicao : '';
                                                if (item.condicao.condicao.condicao.condicao.condicao.condicao.hasOwnProperty('condicao')) {
                                                    if ((item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.valor == false || item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.valor == "false") && value == 0) {
                                                        value = false;
                                                    }
                                                    condicao_seven = eval(`"${value ? value : 0}" ${item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.operador} "${item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.valor}" `);
                                                    composicao_seven = item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.composicao ? item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.composicao : '';
                                                    if (item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.hasOwnProperty('condicao')) {
                                                        if ((item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.valor == false || item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.valor == "false") && value == 0) {
                                                            value = false;
                                                        }
                                                        condicao_eight = eval(`"${value ? value : 0}" ${item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.operador} "${item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.valor}" `);
                                                        composicao_eight = item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.composicao ? item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.composicao : '';
                                                        if (item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.hasOwnProperty('condicao')) {
                                                            if ((item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.valor == false || item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.valor == "false") && value == 0) {
                                                                value = false;
                                                            }
                                                            condicao_nine = eval(`"${value ? value : 0}" ${item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.operador} "${item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.valor}" `);
                                                            composicao_nine = item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.composicao ? item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.composicao : '';
                                                            if (item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.hasOwnProperty('condicao')) {
                                                                if ((item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.valor == false || item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.valor == "false") && value == 0) {
                                                                    value = false;
                                                                }
                                                                condicao_ten = eval(`"${value ? value : 0}" ${item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.operador} "${item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.valor}" `);
                                                                composicao_ten = item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.composicao ? item.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.condicao.composicao : '';
                                                            } else {
                                                                condicao_ten = '';
                                                            }
                                                        } else {
                                                            condicao_nine = '';
                                                            condicao_ten = '';
                                                        }
                                                    } else {
                                                        condicao_eight = '';
                                                        condicao_nine = '';
                                                        condicao_ten = '';
                                                    }
                                                } else {
                                                    condicao_seven = '';
                                                    condicao_eight = '';
                                                    condicao_nine = '';
                                                    condicao_ten = '';
                                                }
                                            } else {
                                                condicao_six = '';
                                                condicao_seven = '';
                                                condicao_eight = '';
                                                condicao_nine = '';
                                                condicao_ten = '';
                                            }
                                        } else {
                                            condicao_five = '';
                                            condicao_six = '';
                                            condicao_seven = '';
                                            condicao_eight = '';
                                            condicao_nine = '';
                                            condicao_ten = '';
                                        }
                                    } else {
                                        condicao_four = '';
                                        condicao_five = '';
                                        condicao_six = '';
                                        condicao_seven = '';
                                        condicao_eight = '';
                                        condicao_nine = '';
                                        condicao_ten = '';
                                    }
                                } else {
                                    condicao_three = '';
                                    condicao_four = '';
                                    condicao_five = '';
                                    condicao_six = '';
                                    condicao_seven = '';
                                    condicao_eight = '';
                                    condicao_nine = '';
                                    condicao_ten = '';

                                }
                            } else {
                                condicao_two = '';
                                condicao_three = '';
                                condicao_four = '';
                                condicao_five = '';
                                condicao_six = '';
                                condicao_seven = '';
                                condicao_eight = '';
                                condicao_nine = '';
                                condicao_ten = '';
                            }

                            if ((item.condicao.valor == false || item.condicao.valor == "false") && value == 0) {
                                value = false;
                            }
                            const condicao = eval(`"${value !== undefined ? value : 0}" ${item.condicao.operador} "${item.condicao.valor}" `);
                            composicao_one = item.condicao.composicao ? item.condicao.composicao : '';

                            // const condicao = eval(`"${ value ? value : 0 }" ${ item.condicao.operador } "${ item.condicao.valor }" `);
 
                            let active = eval(`${condicao} ${composicao_one} ${condicao_two} ${composicao_two} ${condicao_three} ${composicao_three} ${condicao_four} ${composicao_four} ${condicao_five} ${composicao_five} ${condicao_six} ${composicao_six} ${condicao_seven} ${composicao_seven} ${condicao_eight} ${composicao_eight} ${condicao_nine} ${composicao_nine} ${condicao_ten}`);
                           // (condicao && condicao_two && condicao_three && condicao_four && condicao_five && condicao_six && condicao_seven && condicao_eight && condicao_nine && condicao_ten) ? el.slideDown("fast") : el.slideUp("fast");
                            active ? el.slideDown("fast") : el.slideUp("fast");
                           // (condicao || condicao_two || condicao_three || condicao_four) ? el.slideDown("fast") : el.slideUp("fast");
                        }

                    } else {
                        el.slideUp("fast");
                    }
                });
            }

            this.fillValue(initialValue, currentValue);
        },
        updateAttr() {
            try {
                if (!this.isEditable) {
                    throw "O componente em questão não está permitido para edição.";
                }

                axios
                    .post(`${$base_url}cad_item_attr/ajax_save_attr`, {
                        ...this.params, attr: this.attr
                    })
                    .then((response) => {
                        return response.data;
                    })
                    .then(({ data }) => {
                        if (data) {
                            this.$emit('updated');
                            this.filledValues = [];
                        }
                    })
                    .catch((err) => {
                        if (err.response) {
                            const errorMessage = err.response.data.msg;
                            console.error('Error:', errorMessage);
                            this.showDangerMessage(errorMessage);
                        } else {
                            console.error('Error:', err.message);
                            this.showDangerMessage("Ocorreu um erro inesperado.");
                        }
                    });
            } catch (err) {
                console.error(err);

                this.showDangerMessage();
            }
        },
        processaCondicionadosDiana(item, idx) {

            let currentValue = this.attr.condicionados[idx].atributo.dbdata.codigo;
            const isEmpty = currentValue === null || currentValue === undefined || currentValue === '' || (Array.isArray(currentValue) && currentValue.length === 0);


        //     let condicionados_vazios = false;
        //  //   this.attr.forEach((attr1, key) => {
        //         let attr1 = this.attr;
        //         let val_db = attr1.dbdata ? attr1.dbdata.codigo : null;
        //         if (item)
        //         {
        //             item.forEach((item, idx) => {
        //                 let val_cond = item.condicao.valor;

        //                 if (val_cond == true || val_cond == "true" || val_cond == "1") {
        //                     val_cond = 1;
        //                 } else if (val_cond == false || val_cond == "false" || val_cond == "0") {
        //                     val_cond = 0;
        //                 }
 
        //                 if (val_cond == val_db) {
        //                     let containerId = `${item.atributo.codigo}`;
        //                     let $inputElement = $(`#${containerId} input`);
        //                     let $selectElement = $(`#${containerId} select`);
        //                     console.log(`codigo ${item.atributo.codigo} val: ${item.atributo.dbdata.codigo}  `);
        //                     console.log(item);
        //                     if ((item.atributo.dbdata.codigo != null && item.atributo.dbdata.codigo != undefined && item.atributo.dbdata.codigo != '')  
        //                     ){
        //                         condicionados_vazios = true
        //                     } 
        //                 } 
        //             });
        //         }
  
           
        //    if (condicionados_vazios != true)
        //    {
        //        if (!isEmpty) { return; }
        //     }
        //     if (!isEmpty) { return; }

            this.isDianaLoading = true;
            this.dangerMessage = false;
            const codigoParaEnviar = this.attr.condicionados[idx].atributo.codigo;
            if (this.attrs_processados_diana.includes(`${codigoParaEnviar}`)) {
                return;
            }
            const integracaoApiUrl = `/wf/atributos/requestDiana`;
            let descricaoPayload = this.itemDescription || "N/A";

            if (descricaoPayload === "N/A" && typeof $ === 'function') { descricaoPayload = this.registro_item || "N/A"; }

            const payload = {
                "descricao": this.registro_item,
                "codigo": codigoParaEnviar,
                "checar_info_na_desc": false,
                "busca_trechos": false,
                "busca_razao": false,
                "request_email": this.email_usuario
            };
            //console.log(`processaCondicionadosDiana ${codigoParaEnviar}`);
            try {
                axios.post(integracaoApiUrl, payload).then((response) => {
                    this.attrs_processados_diana.push(`${codigoParaEnviar}`);
                    if (response.data.hasOwnProperty('valor')) {
                        let valorRecebido = response.data.valor;
                        if (valorRecebido !== null && valorRecebido !== undefined && String(valorRecebido).trim() !== '') {
                            if (valorRecebido == true || valorRecebido == "true" || valorRecebido == "1") {
                                valorRecebido = 1;
                            } else if (valorRecebido == false || valorRecebido == "false" || valorRecebido == "0") {
                                valorRecebido = 0;
                            }

                            if (!this.attr.condicionados[idx].atributo.dbdata) this.$set(this.attr.condicionados[idx].atributo, 'dbdata', {});
                            // $(`#${codigoParaEnviar}`).removeClass('api-filled-border').addClass('api-filled-border');

                            this.$set(this.attr.condicionados[idx].atributo.dbdata, 'codigo', String(valorRecebido));
                            this.$set(this.attr.condicionados[idx].atributo.dbdata, 'filledByApi', true);

                            this.changed = true;
                            if (this.alerts == false)
                            {
                               // swal("Sucesso!", "Sugestão Diana aplicada.", "success");
                                this.alerts = true;
                            }
                        } else {   }
                    } else { this.showDangerMessage(`Resposta inválida (sem valor) para ${codigoParaEnviar}.`); }
                });

            } catch (error) {
                const errorMsg = error.response.data.erro || error.message || "Erro API Diana.";
                this.showDangerMessage(`Erro Diana [${codigoParaEnviar}]: ${errorMsg}`);
            } finally {


                this.isDianaLoading = false;
                return;
            }
        },
        async chamarDiana() {
 
            this.attrs_processados_diana = [];
            if (this.isDianaLoading || !this.isEditable) return;
            const currentValue = this.attr.dbdata.codigo;
            console.log(currentValue);
            const isEmpty = currentValue === null || currentValue === undefined || currentValue === '' || (Array.isArray(currentValue) && currentValue.length === 0);
           
           let condicionados_vazios = false;
         //   this.attr.forEach((attr1, key) => {
                let attr1 = this.attr;
                let val_db = attr1.dbdata ? attr1.dbdata.codigo : null;
                if (attr1.condicionados)
                {
                    attr1.condicionados.forEach((item, idx) => {
                        let val_cond = item.condicao.valor;

                        if (val_cond == true || val_cond == "true" || val_cond == "1") {
                            val_cond = 1;
                        } else if (val_cond == false || val_cond == "false" || val_cond == "0") {
                            val_cond = 0;
                        }
 
                        if (val_cond == val_db) {
                            let containerId = `${item.atributo.codigo}`;
                            let $inputElement = $(`#${containerId} input`);
                            let $selectElement = $(`#${containerId} select`);
                            // console.log(`codigo ${item.atributo.codigo} val: ${item.atributo.dbdata.codigo}  `);
                            // console.log(item);
                            if ((item.atributo.dbdata.codigo == null || item.atributo.dbdata.codigo == undefined || item.atributo.dbdata.codigo == ''
                            || item.atributo.dbdata.codigo == NaN  )  
                            ){
                                this.processaCondicionadosDiana(item, idx);
                                condicionados_vazios = true
                            } 
                        } 
                    });
                }
          //  });
 
 
        if (condicionados_vazios == true && !isEmpty)
        {
            swal("Sucesso!", "Sugestão Diana aplicada.", "success");
            return;
        }
        if (!isEmpty)
        {
            swal("Sucesso!", "Sugestão Diana aplicada.", "success");
            return;
        }
        if (!isEmpty) { return; }
        
            this.isDianaLoading = true;
            this.dangerMessage = false;
            const codigoParaEnviar = this.attr.codigo;

            const integracaoApiUrl = `/wf/atributos/requestDiana`;
            let descricaoPayload = this.itemDescription || "N/A";
            if (this.attrs_processados_diana.includes(`${codigoParaEnviar}`)) {
                return;
            }
            if (descricaoPayload === "N/A" && typeof $ === 'function') { descricaoPayload = this.registro_item || "N/A"; }

            const payload = {
                "descricao": this.registro_item,
                "codigo": codigoParaEnviar,
                "checar_info_na_desc": false,
                "busca_trechos": false,
                "busca_razao": false,
                "request_email": this.email_usuario
            };
          //  console.log(`chamarDiana ${codigoParaEnviar}`);
            try {
                const response = await axios.post(integracaoApiUrl, payload);
                this.attrs_processados_diana.push(`${codigoParaEnviar}`);
                if (response.data.hasOwnProperty('valor')) {
                    let valorRecebido = response.data.valor;
                    if (valorRecebido !== null && valorRecebido !== undefined && String(valorRecebido).trim() !== '') {

                        if (valorRecebido == true || valorRecebido == "true" || valorRecebido == "1") {
                            valorRecebido = 1;
                        } else if (valorRecebido == false || valorRecebido == "false" || valorRecebido == "0") {
                            valorRecebido = 0;
                        }

                        if (!this.attr.dbdata) this.$set(this.attr, 'dbdata', {});
                        $(`#${codigoParaEnviar}`).removeClass('api-filled-border').addClass('api-filled-border');

                        this.$set(this.attr.dbdata, 'codigo', String(valorRecebido));
                        this.$set(this.attr.dbdata, 'filledByApi', true);
                        this.changed = true;
                        this.$nextTick(() => {
                            this.handleAttributeConditioning(this.attr.dbdata.codigo);
                        });
                        if (!this.attr.condicionados) {
                            // swal("Sucesso!", "Sugestão Diana aplicada.", "success");
                        }
                    } else { this.showDangerMessage(`Diana não retornou valor para ${codigoParaEnviar}.`); }
                } else { this.showDangerMessage(`Resposta inválida (sem valor) para ${codigoParaEnviar}.`); }
            } catch (error) {
                const errorMsg = error.response.data.erro || error.message || "Erro API Diana.";
                this.showDangerMessage(`Erro Diana [${codigoParaEnviar}]: ${errorMsg}`);
            } finally {

                if (this.attr.condicionados) {
                    // 
                    let val_db = this.attr.dbdata.codigo;
          
                    // console.log('this.attr',this.attr);
                    // console.log('this.attr.condicionados',this.attr.condicionados);
                    this.attr.condicionados.forEach((item, idx) => {
                        let val_cond = item.condicao.valor;

                        if (val_cond == true || val_cond == "true" || val_cond == "1") {
                            val_cond = 1;
                        } else if (val_cond == false || val_cond == "false" || val_cond == "0") {
                            val_cond = 0;
                        }
                     //   console.log(`val_cond ${val_cond} val_db ${val_db} item ${item}`);
                        if (val_cond == val_db) {
                          // console.log(`val_cond ${val_cond} val_db ${val_db} item ${item}`);
                            this.processaCondicionadosDiana(item, idx);
                        } 

                    });

                }
                this.isDianaLoading = false;
                this.alerts = false;
            }
            swal("Sucesso!", "Sugestão Diana aplicada.", "success");
        },
        handleAttributeConditioning(currentValue) {
            if (!this.attr.atributoCondicionante || !this.attr.condicionados) return;
            let value = currentValue;
            const inputType = this.attr.formaPreenchimento.toUpperCase();
            this.attr.condicionados.forEach((item) => {
                if (!item.atributo.codigo || !item.condicao) return;
                const el = $(`#${item.atributo.codigo}`);
                if (!el || el.length === 0) return;
                let conditionMet = false;
                try {
                } catch (e) { console.error("Erro condição", e); }
                conditionMet ? el.stop().slideDown("fast") : el.stop().slideUp("fast");
            });
        },
        showSuccessMessage() {
            this.successMessage = true;

            setTimeout(() => {
                this.successMessage = false;
            }, 10000);
        },
        showDangerMessage(message = "Algo deu errado durante o processamento do atributo.") {
            this.dangerMessage = true;
            this.dangerText = message;

            setTimeout(() => {
                this.dangerMessage = false;
            }, 10000);
        },
        hasParent() {
            return this.parentAttr ? true : false;
        },
        attrWarnings() {
            const warns = [];

            if (this.attr.orientacaoPreenchimento) {
                const mustToHaveChilds = this.attr.orientacaoPreenchimento.toUpperCase().includes("Caso seja selecionado \"99 - Outros\" será necessário informar".toUpperCase());

                if (mustToHaveChilds && !this.attr.atributoCondicionante) {
                    warns.push(this.attr.orientacaoPreenchimento);
                }
            }

            return warns;
        }
    },
    computed: {
        hasValueChanged: function () {
            const bool = this.changed && !this.hasParent();
            return bool;
        },
        templateClass: function () {
            return { 'template': this.hasParent() }
        },
        panelClass: function () {
            return { 'panel-custom': this.hasParent(), 'panel-default': !this.hasParent() }
        },
        panelHeadingClass: function () {
            return { 'pe-0': this.hasParent() }
        },
        attrColClass: function () {
            return { 'pe-0': this.hasParent() }
        },
        panelBodyClass: function () {
            return {
                'pt-0 pe-0': this.hasParent(),
                'border border-success': this.hasValueChanged && this.isEditable,
                'border border-warn': !this.hasValueChanged && !this.hasParent() && this.isEditable,
            }
        },
        saveCheckClass: function () {
            // Esta função agora usará o 'hasValueChanged' corrigido
            return {
                'check check-success': true,
            }
        },
        templateClass: function () {
            return {
                'template': this.hasParent()
            }
        },
        panelClass: function () {
            return {
                'panel-custom': this.hasParent(),
                'panel-default': !this.hasParent(),
            }
        },
        panelHeadingClass: function () {
            return {
                'pe-0': this.hasParent(),
            }
        },
        attrColClass: function () {
            return {
                'pe-0': this.hasParent(),
            }
        },
        panelBodyClass: function () {
            return {
                'pt-0 pe-0': this.hasParent(),
                'border border-success': this.hasValueChanged && this.isEditable,
                'border border-warn': !this.hasValueChanged && !this.hasParent() && this.isEditable,
            }
        },
    },
    beforeMount() {
        this.attr = this.initialAttr;
        if (this.attr.formaPreenchimento.toUpperCase() === "BOOLEANO" && this.attr.dbdata.codigo > 0) {
            this.attr.dbdata.codigo = "1";
        }
    },
    mounted() {
        $('[data-toggle="tooltip"]').tooltip();
        this.descricao_item = this.registro_item;
    },
    components: {
        CadItemAttrInput
    },
    watch: {
        initialAttr: {
            handler(newVal, oldVal) {
                if (!_.isEqual(newVal, oldVal)) {
                    this.attr = _.cloneDeep(newVal);
                    if (!this.attr.dbdata) this.attr.dbdata = { codigo: '' };
                    this.changed = false;
                    this.handleAttributeConditioning(this.attr.dbdata.codigo);
                }
            },
            deep: true
        },
        currentValue(oldVal, newVal) {
            if (oldVal != newVal) {
                this.changed = true;
            } else {
                this.changed = false;
            }
        }
    }
}
</script>

<template>
    <div :class="`${addClass} ${templateClass}`" :key="key" :id="attr.codigo" v-if="attr">
        <div class="panel" :class="panelClass">
            <div class="panel-heading px-4" :class="panelHeadingClass">
                <div class="d-flex justify-content-between">
                    <div class="px-4">
                        <span>
                            <!-- <strong :class="{ 'red-text': attr.dbdata.obrigatorio == true }">{{ attr.codigo }} - {{ attr.nomeApresentacao }} <template v-if="attr.dbdata.obrigatorio == true" :class="{ 'red-text': attr.dbdata.obrigatorio == true }">*</template></strong> -->
                            <strong :class="{ 'red-text': attr.obrigatorio == true }">
                                {{ attr.codigo }} - {{ attr.nomeApresentacao }}
                                <template v-if="(attr.obrigatorio == true)"
                                    :class="{ 'red-text':  attr.obrigatorio == true }">*</template>
                            </strong>
                        </span>
                        <span v-if="attr.orientacaoPreenchimento">
                            <strong>| Orientações de preenchimento: </strong>{{ attr.orientacaoPreenchimento }}
                        </span>
                    </div>
                    <a href="#" data-toggle="tooltip" data-placement="left" data-html="true" class="px-4"
                        :title="showInfo({ attr: attr })">
                        <i class="glyphicon glyphicon-info-sign primary"></i>
                    </a>
                </div>
            </div>
            <div class="panel-body" :class="panelBodyClass">
                <div class="container-fluid">
                    <div class="row d-flex">
                        <div class="col-sm-12">
                            <div class="row" v-if="attrWarnings().length > 0">
                                <div class="col-sm-12">
                                    <div class="alert alert-warning">
                                        <strong>Parece que não é possivel atribuir as seguintes regras ao atributo:</strong>
                                        <ul v-for="warn in attrWarnings()">
                                            <li>{{ warn }}</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="row" v-show="successMessage">
                                <div class="col-sm-12">
                                    <div class="alert alert-anim alert-success">
                                        Atributo <strong>{{ attr.codigo }}</strong> atualizado com sucesso.
                                    </div>
                                </div>
                            </div>
                            <div class="row" v-show="dangerMessage">
                                <div class="col-sm-12">
                                    <div class="alert alert-anim alert-danger">
                                        {{ dangerText }}
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12" :class="attrColClass">
                                    <CadItemAttrInput v-if="attr.formaPreenchimento" :initialAttr="attr"
                                        :initialValue="attr.dbdata.codigo" :key="key" @onHandleSelect="handleSelect"
                                        @onHandleChange="handleChange"></CadItemAttrInput>
                                    <div v-if="attr.atributoCondicionante">
                                        <CadItemAttr v-for="(cond, index) in attr.condicionados" :key="index"
                                            :ref="attr.codigo" :params="params" :initialAttr="cond.atributo"
                                            :parentAttr="attr" :isEditable="isEditable" @onFilledValue="filledValue"
                                            hidden="true"></CadItemAttr>
                                    </div>

                                    <div v-if="attr.formaPreenchimento.toUpperCase() === 'COMPOSTO'">
                                        <CadItemAttr v-for="(childAttr, index) in attr.listaSubatributos" :key="index"
                                            :params="params" :initialAttr="childAttr" :parentAttr="attr"
                                            @onFilledValue="filledValue" :isEditable="isEditable"></CadItemAttr>
                                    </div>

                                    <div v-if="!hasParent()">
                                        <input type="hidden" name="raw_attr[]" :value="JSON.stringify(attr)" />
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="px-4 d-flex align-items-middle" v-if="isEditable && !parentAttr">
                            <a :class="saveCheckClass" data-toggle="tooltip" data-placement="top" title=""
                                data-original-title="Atualizar" @click="updateAttr">
                                <i class="glyphicon glyphicon-check"></i>
                            </a>
                        </div>

                        <div v-if="integra_diana_company && !parentAttr" class="px-4 d-flex align-items-middle">
                            <a v-if="isEditable && !parentAttr" href="#" class="action-icon diana-trigger"
                                :class="{ 'loading': isDianaLoading }" data-toggle="tooltip" data-placement="top"
                                title="Sugestão Diana" @click.prevent="chamarDiana"> <span v-if="isDianaLoading"
                                    class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <i v-else class="glyphicon glyphicon-pencil"></i> </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>